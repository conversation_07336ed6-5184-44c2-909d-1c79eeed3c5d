import type { NextConfig } from "next";

const nextConfig: NextConfig = {

    images: {
        remotePatterns: [
            {
                protocol: "https",
                hostname: "s3-ap-south-1.amazonaws.com",
            },
            {
                protocol: "https",
                hostname: "avatars.githubusercontent.com",
            },
            {
                protocol: "https",
                hostname: "user-images.githubusercontent.com",
            },
            {
                protocol: "https",
                hostname: "images.opencollective.com",
            },
        ],
    },

};

export default nextConfig;
