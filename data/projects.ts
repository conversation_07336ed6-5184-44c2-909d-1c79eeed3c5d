export interface Project {
  id: string
  title: string
  description: string
  longDescription?: string
  image: string
  githubUrl: string
  demoUrl: string
  tags?: string[]
  features?: string[]
  technologies?: string[]
  date?: string
}

export const projects: Project[] = [
   {
    id: "saas",
    title: "Profolify – Portfolio Builder Web App ",
    description: "Portfolio Builder is a revolutionary portfolio creation platform that empowers professionals to build stunning, responsive portfolios with zero coding required.",
    longDescription: "Portfolio Builder is a revolutionary portfolio creation platform that empowers professionals to build stunning, responsive portfolios with zero coding required. Built with cutting-edge technology and featuring our groundbreaking Live DOM Capture Export System, Portfolio Builder delivers pixel-perfect static websites that work anywhere.",
    image: "/builder.png",
    githubUrl: "",
    demoUrl: "https://www.profolify.com/",
    tags: ["Portfolio", "Next.js", "TypeScript", "Tailwind CSS", "UI/UX", "Saas"],
    features: [
      "Protfolio Editor",
      "Real-time Inline Editing",
      "Smart URL Generation",
      "Professional Theme System",
      "Live DOM Capture Export"
    ],
    technologies: [
      "Next.js", "TypeScript", "Tailwind CSS", "Firebase", "Cloudinary"
    ],
    date: "2025-07"
  },
  {
    id: "blogapp",
    title: "SEO-Optimized Markdown Blog with Dark Mode & Newsletter Integration",
    description: "A modern, SEO-optimized blog built with Next.js and TypeScript, featuring dark mode, markdown-based content, and responsive design.",
    longDescription: "A modern, SEO-optimized blog built with Next.js and TypeScript, featuring dark mode, markdown-based content, and responsive design. Integrated with SendGrid for contact forms, ConvertKit for newsletters, and social sharing on Facebook, Twitter, and LinkedIn.",
    image: "/blog.png",
    githubUrl: "https://github.com/darshanbajgain/darshan-blog-temp",
    demoUrl: "https://blog.darshanbajgain.com.np/",
    tags: ["Next.js", "TypeScript", "Markdown", "SEO", "Dark Mode"],
    features: [
      "Markdown content management",
      "Dark mode support",
      "Newsletter integration",
      "Contact form with SendGrid",
      "Social sharing capabilities",
      "Responsive design"
    ],
    technologies: [
      "Next.js", "TypeScript", "Tailwind CSS", "SendGrid", "ConvertKit", "Markdown"
    ],
    date: "2023-09"
  },
  {
    id: "dashboard",
    title: "Educational Institution Admin Dashboard",
    description: "This application provides a comprehensive interface for school administrators to manage students, track admissions, and organize school events in the calendar.",
    longDescription: "This application provides a comprehensive interface for school administrators to manage students, track admissions, and organize school events in the calendar. Features include student management, admission tracking, event scheduling, and data visualization.",
    image: "/dashboard.png",
    githubUrl: "https://github.com/darshanbajgain/edu-insights-dashboard",
    demoUrl: "https://edu-insights-dashboard.vercel.app/",
    tags: ["Dashboard", "React", "Calendar", "Admin", "Education"],
    features: [
      "Student management",
      "Admission tracking",
      "Calendar events",
      "Data visualization",
      "User roles and permissions"
    ],
    technologies: [
      "React", "JavaScript", "Chart.js", "FullCalendar", "CSS3"
    ],
    date: "2023-02"
  },
  {
    id: "radio",
    title: "Web FM Radio Player",
    description: "AllNepalFM is a modern, responsive web application that provides access to publicly available Nepali radio stations.",
    longDescription: "AllNepalFM is a modern, responsive web application that provides access to publicly available Nepali radio stations. The platform allows users to discover, browse, and listen to FM radio stations from across Nepal, organized by province and category.",
    image: "/nepalifm.png",
    githubUrl: "https://github.com/darshanbajgain/allnepalfm",
    demoUrl: "https://allnepalfm.vercel.app/",
    tags: ["React", "Audio", "API", "Responsive", "Web App"],
    features: [
      "Browse radio stations by province",
      "Search functionality",
      "Audio streaming",
      "Responsive design",
      "Favorites system"
    ],
    technologies: [
      "React", "JavaScript", "HTML5 Audio API", "CSS3", "LocalStorage"
    ],
    date: "2023-06"
  },
  {
    id: "landing",
    title: "MODERNO, Clothing E-commerce Site",
    description: "MODERNO is a modern, responsive e-commerce website template for clothing brands built with Next.js, TypeScript, and Tailwind CSS.",
    longDescription: "MODERNO is a modern, responsive e-commerce website template for clothing brands built with Next.js, TypeScript, and Tailwind CSS. It features a clean, minimalist design focused on showcasing products with an emphasis on user experience.",
    image: "/moderno.png",
    githubUrl: "https://github.com/darshanbajgain/modern-basic-clothing-brand-website",
    demoUrl: "https://v0-clothing-brand-website-black.vercel.app/",
    tags: ["E-commerce", "Next.js", "TypeScript", "Tailwind CSS", "UI/UX"],
    features: [
      "Product showcase",
      "Category filtering",
      "Responsive design",
      "Cart functionality",
      "User authentication"
    ],
    technologies: [
      "Next.js", "TypeScript", "Tailwind CSS", "React Context API", "LocalStorage"
    ],
    date: "2023-04"
  },

  {
    id: "website",
    title: "Personal Portfolio Website",
    description: "Responsive Single Page Personal Portfolio Website Using HTML, JavaScript and Tailwind CSS.",
    longDescription: "A responsive single-page portfolio website showcasing my skills, projects, and experience. Built with HTML, JavaScript, and Tailwind CSS, featuring smooth scrolling, dark mode, and contact form integration.",
    image: "/websitepfp2.png",
    githubUrl: "https://github.com/darshanbajgain/Perosnal-Website",
    demoUrl: "https://darshanbajgain.github.io/Perosnal-Website/",
    tags: ["Portfolio", "HTML", "JavaScript", "Tailwind CSS", "Responsive"],
    features: [
      "Responsive design",
      "Project showcase",
      "Skills section",
      "Contact form",
      "Smooth scrolling"
    ],
    technologies: [
      "HTML5", "JavaScript", "Tailwind CSS", "CSS3", "FormSubmit"
    ],
    date: "2022-11"
  },


]
