import { create } from "zustand"
import { persist } from "zustand/middleware"

interface ThemeState {
    theme: "light" | "dark"
    toggleTheme: () => void
}

export const useThemeStore = create<ThemeState>()(
    persist(
        (set) => ({
            theme: "dark", // default theme
            toggleTheme: () =>
                set((state) => ({
                    theme: state.theme === "dark" ? "light" : "dark",
                })),
        }),
        {
            name: "theme-storage",
        },
    ),
)