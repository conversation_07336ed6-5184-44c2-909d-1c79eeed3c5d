"use client"

import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import React from "react"

interface SectionWrapperProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  title?: string
  className?: string
  contentClassName?: string
  withBeam?: boolean
  beamProps?: {
    duration?: number
    size?: number
    colorFrom?: string
    colorTo?: string
    className?: string
    reverse?: boolean
  }
}

export function SectionWrapper({
  children,
  title,
  className,
  contentClassName,
  ...props
}: SectionWrapperProps) {
  return (
    <section
      className={cn(
        "w-full backdrop-blur-sm py-16 lg:py-20 overflow-hidden",
        className
      )}
      {...props}
    >
      <div className="w-full min-w-80 max-w-screen-lg mx-auto px-4 sm:px-6 lg:px-8">
        {title && (
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-12 text-center lg:text-left relative inline-block"
          >
            <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
              {title}
            </span>
            <span className="absolute -bottom-3 left-0 w-16 h-1 bg-gradient-to-r from-primary to-primary/60 rounded-full"></span>
          </motion.h2>
        )}

        <div className={cn("relative z-10", contentClassName)}>
          {children}
        </div>
      </div>
    </section>
  )
}
