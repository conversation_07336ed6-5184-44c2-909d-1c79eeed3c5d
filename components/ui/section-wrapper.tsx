"use client"

import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import React from "react"

interface SectionWrapperProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  title?: string
  className?: string
  contentClassName?: string
  withBeam?: boolean
  beamProps?: {
    duration?: number
    size?: number
    colorFrom?: string
    colorTo?: string
    className?: string
    reverse?: boolean
  }
}

export function SectionWrapper({
  children,
  title,
  className,
  contentClassName,
  ...props
}: SectionWrapperProps) {
  return (
    <section
      className={cn(
        "w-full bg-card/80 backdrop-blur-sm py-12 overflow-hidden",
        className
      )}
      {...props}
    >
      <div className="w-full min-w-80 max-w-screen-lg mx-auto px-6 sm:px-8">
        {title && (
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-2xl sm:text-3xl font-medium mb-8 text-primary relative inline-block"
          >
            {title}
            <span className="absolute -bottom-2 left-0 w-12 h-1 bg-primary rounded-full"></span>
          </motion.h2>
        )}

        <div className={cn("relative z-10", contentClassName)}>
          {children}
        </div>


      </div>
    </section>
  )
}
