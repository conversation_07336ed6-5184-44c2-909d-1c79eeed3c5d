"use client"

import { useState } from "react"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronDown, ChevronUp } from "lucide-react"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"
import { SectionWrapper } from "@/components/ui/section-wrapper"

const tools = [
  {
    name: "Next.js",
    description: "React Framework",
    icon: "/next.svg",
  },
  {
    name: "React",
    description: "JavaScript Library",
    icon: "https://raw.githubusercontent.com/devicons/devicon/master/icons/react/react-original.svg",
  },
  {
    name: "TypeScript",
    description: "JavaScript with Types",
    icon: "https://raw.githubusercontent.com/devicons/devicon/master/icons/typescript/typescript-original.svg",
  },
  {
    name: "JavaScript",
    description: "Programming Language",
    icon: "https://raw.githubusercontent.com/devicons/devicon/master/icons/javascript/javascript-original.svg",
  },
  {
    name: "Shadcn/ui",
    description: "UI Component Library",
    icon: "https://avatars.githubusercontent.com/u/139895814?s=200&v=4",
  },
  {
    name: "Recharts",
    description: "Data Visualization for React",
    icon: "https://images.opencollective.com/recharts/logo/256.png?height=256",
  },
  {
    name: "Material-UI",
    description: "React UI Framework",
    icon: "https://raw.githubusercontent.com/devicons/devicon/master/icons/materialui/materialui-original.svg",
  },
  {
    name: "GitHub",
    description: "Version Control",
    icon: "https://raw.githubusercontent.com/devicons/devicon/master/icons/github/github-original.svg",
  },
  {
    name: "Jenkins",
    description: "CI/CD Platform",
    icon: "https://raw.githubusercontent.com/devicons/devicon/master/icons/jenkins/jenkins-original.svg",
  },
  {
    name: "Java Spring Boot",
    description: "Spring Framework",
    icon: "https://spring.io/img/projects/spring-boot.svg",
  },
]

const ToolCard = ({ tool }: { tool: (typeof tools)[0] }) => (
  <motion.div
    layout
    initial={{ opacity: 0, scale: 0.9 }}
    animate={{ opacity: 1, scale: 1 }}
    exit={{ opacity: 0, scale: 0.9 }}
    transition={{ duration: 0.3 }}
    whileHover={{ y: -5 }}
  >
    <Card className="group cursor-default border border-primary/10 bg-background hover:bg-accent/50 transition-all duration-300 h-full shadow-sm hover:shadow-md">
      {/* Desktop View */}
      <div className="hidden md:flex p-6 items-start gap-4 h-full">
        <div className="relative h-16 w-16 rounded-full overflow-hidden bg-white shadow-sm border border-primary/10">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 z-0"></div>
          <Image
            src={tool.icon || "/placeholder.svg"}
            alt={tool.name}
            fill
            className="object-contain p-3 z-10 transition-transform duration-500 group-hover:scale-110"
          />
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors duration-300">{tool.name}</h3>
          <p className="text-sm text-muted-foreground mt-1">{tool.description}</p>
        </div>
      </div>

      {/* Mobile View */}
      <div className="md:hidden flex items-center justify-start gap-3 p-4">
        <div className="relative h-10 w-10 rounded-full overflow-hidden bg-white p-1.5 border border-primary/10 shadow-sm">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 z-0"></div>
          <Image
            src={tool.icon || "/placeholder.svg"}
            alt={tool.name}
            fill
            className="object-contain p-1 z-10 transition-transform duration-500 group-hover:scale-110"
          />
        </div>
        <h3 className="font-medium text-sm text-foreground group-hover:text-primary transition-colors duration-300">{tool.name}</h3>
      </div>
    </Card>
  </motion.div>
)

export default function Tools() {
  const [isExpanded, setIsExpanded] = useState(false)
  const initialDisplayCount = 6
  const displayedTools = isExpanded ? tools : tools.slice(0, initialDisplayCount)

  return (
    <SectionWrapper title="Tools & Technologies">
      <motion.div layout className="grid grid-cols-1 sm:grid-cols-2 gap-5 p-1">
        <AnimatePresence>
          {displayedTools.map((tool) => (
            <ToolCard key={tool.name} tool={tool} />
          ))}
        </AnimatePresence>
      </motion.div>
      {tools.length > initialDisplayCount && (
        <div className="flex justify-center mt-8">
          <Button
            variant="outline"
            onClick={() => setIsExpanded(!isExpanded)}
            className="group border-primary/20 hover:bg-primary hover:text-primary-foreground transition-all duration-300"
          >
            <span className="mr-2">
              {isExpanded ? "Show Less" : `Show More (${tools.length - initialDisplayCount})`}
            </span>
            {isExpanded ? (
              <ChevronUp className="h-4 w-4 transition-transform group-hover:-translate-y-0.5" />
            ) : (
              <ChevronDown className="h-4 w-4 transition-transform group-hover:translate-y-0.5" />
            )}
          </Button>
        </div>
      )}
    </SectionWrapper>
  )
}

