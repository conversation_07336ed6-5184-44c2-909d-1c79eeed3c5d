"use client"

import Image from "next/image"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import GitHubIcon from "@mui/icons-material/GitHub"
import LinkedInIcon from "@mui/icons-material/LinkedIn"
import XIcon from "@mui/icons-material/X"
import { TagCloud } from "@frank-mayer/react-tag-cloud"
import { BorderBeam } from "../magicui/border-beam"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "../ui/tooltip"
import WebStoriesIcon from '@mui/icons-material/WebStories';
import EditNoteIcon from '@mui/icons-material/EditNote';
import { Download } from "lucide-react"
import { Button } from "../ui/button"

const socialLinks = [
  {
    icon: GitHubIcon,
    href: "https://github.com/darshanbajgain",
    label: "GitHub",
    color: "text-primary",
  },
  {
    icon: XIcon,
    href: "https://x.com/iamthesun77",
    label: "Twitter",
    color: "text-primary",
  },
  {
    icon: LinkedInIcon,
    href: "https://www.linkedin.com/in/devdarshan10/",
    label: "LinkedIn",
    color: "text-primary",
  },
  {
    icon: EditNoteIcon,
    href: "https://blog.darshanbajgain.com.np/",
    label: "Blog",
    color: "text-primary",
  },
]

const skills = [
  "Next.js",
  "React",
  "JavaScript",
  "TypeScript",
  "Tailwind CSS",
  "Git",
  "REST API",
  "Figma",
  "shadcn/ui",
  "Material UI",
  "Zustand",
  "Jenkins",
  "Java",
  "Spring Boot",
  "MySQL",
]



export function Hero() {

  return (
    <section className="w-full bg-card/80 backdrop-blur-sm lg:py-12 overflow-hidden">
      <div className="w-full min-w-80 max-w-screen-lg mx-auto px-6 sm:px-8">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-8">
          {/* Left side: Profile image, name, and social links */}
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="w-full lg:w-2/5 flex flex-col items-center mt-4 mb-6"
          >
            <motion.div
              className="h-28 w-28 sm:h-40 sm:w-40 mb-6 relative group"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="absolute inset-0 rounded-full bg-gradient-to-br from-primary/40 to-secondary/40 blur-md -z-10 group-hover:blur-xl transition-all duration-300"></div>
              <div className="absolute inset-1 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 -z-10"></div>
              <Image
                src="/profile-pic(1).png"
                alt="Darshan Bajgain"
                fill
                className="rounded-full border-2 border-primary/30 object-cover"
                priority
              />
            </motion.div>


            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              className="text-2xl sm:text-3xl md:text-4xl leading-tight font-bold text-primary text-center mb-4"
            >
              Darshan Bajgain
            </motion.h1>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="px-4 py-1.5 rounded-full bg-primary/10 text-primary font-medium text-sm mb-8"
            >
              Frontend Developer
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="flex items-center justify-center gap-5 mb-5"
            >
              <TooltipProvider>
                {socialLinks.map((item, index) => (
                  <motion.div
                    key={item.label}
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.7 + index * 0.1, duration: 0.3 }}
                  >
                    <Tooltip key={item.label}>
                      <TooltipTrigger asChild>
                        <Link
                          href={item.href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className={cn(
                            "p-2.5 rounded-full transition-all duration-300 border border-primary/20",
                            "hover:bg-primary hover:text-white hover:scale-110 hover:border-primary",
                            "bg-card shadow-sm",
                            item.color,
                          )}
                          aria-label={item.label}
                        >
                          <item.icon className="w-5 h-5 sm:w-6 sm:h-6" />
                        </Link>
                      </TooltipTrigger>

                      <TooltipContent
                        side="bottom"
                        sideOffset={10}
                        className="bg-popover text-popover-foreground px-3 py-1.5 text-sm font-medium shadow-md rounded-lg border-none"
                      >
                        {item.label}
                      </TooltipContent>
                    </Tooltip>
                  </motion.div>
                ))}
              </TooltipProvider>
            </motion.div>

            {/* Portfolio button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2, duration: 0.5 }}
              className="mb-6"
            >

              <Button
                variant="outline"
                size="sm"
                className="gap-2 mt-5 border-primary/20 hover:bg-primary hover:text-primary-foreground w-full sm:w-auto transition-all duration-300"
                onClick={() => window.open("darshanbajgainresume.pdf", "_blank")}
              >
                <Download className="h-4 w-4" />
                <span>Download Resume</span>
              </Button>
            </motion.div>
          </motion.div>

          {/* Right side: Bio, skills, experience, and education sliders */}
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="flex flex-col items-center lg:items-start w-full lg:w-3/5"
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.5 }}
              className="w-full p-4 rounded-xl bg-gradient-to-br from-card via-card to-primary/5 border border-primary/10 shadow-sm mb-8 relative"
            >
              <span className="text-2xl leading-8 tracking-wider font-semibold block mb-3 text-primary">👋 Hello there!</span>
              <p className="text-sm sm:text-base text-secondary leading-relaxed text-left mb-4">
                I&apos;m Darshan, a Frontend Developer who loves turning designs into clean, functional code. Passionate
                about building smooth, interactive web experiences.
              </p>
              <div className="flex flex-col justify-center lg:flex-row items-center gap-4">
                <Link
                  href="/portfolio"
                  className="text-sm text-primary font-medium hover:underline flex items-center gap-1.5 group p-2.5 rounded-full transition-all duration-300 border border-primary/20
                            hover:bg-primary hover:text-white hover:border-primary
                            bg-card shadow-sm"
                >
                  <WebStoriesIcon className="w-4 h-4  " />
                  <span>Check out my portfolio</span>
                </Link>

                <Link
                  href="https://blog.darshanbajgain.com.np/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-primary font-medium hover:underline flex items-center gap-1.5 group p-2.5 rounded-full transition-all duration-300 border border-primary/20
                            hover:bg-primary hover:text-white hover:border-primary
                            bg-card shadow-sm"
                  onClick={() => {
                    // Update active hash in localStorage to help with navbar selection
                    if (typeof window !== 'undefined') {
                      localStorage.setItem('activeNavItem', '/portfolio');
                    }
                  }}
                >
                  <EditNoteIcon className="w-4 h-4 " />
                  <span>Check out my blog</span>
                </Link>
              </div>

              {/* Enhanced border beam effect */}
              <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-xl">
                <BorderBeam
                  duration={8}
                  size={600}
                  colorFrom="hsl(var(--primary))"
                  colorTo="hsl(var(--secondary))"
                  className="opacity-30 rounded-xl" />
              </div>
            </motion.div>
            <div className="relative h-[300px] w-full overflow-hidden rounded-xl">

              <AnimatePresence mode="sync">
                <motion.div
                  key="skills"
                  initial={{ opacity: 0, x: 300 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -300 }}
                  transition={{ duration: 0.5 }}
                  className="absolute inset-0 flex flex-col items-center lg:items-start justify-start pt-16 px-4  my-8"
                >

                  <div className="w-full flex items-center justify-center lg:justify-start">
                    <TagCloud
                      className="text-gray-900 dark:text-white text-center font-medium text-sm"
                      options={(w: Window & typeof globalThis) => ({
                        radius: Math.min(150, w.innerWidth / 2, w.innerHeight / 2),
                        maxSpeed: "normal",
                        initSpeed: "slow",
                        direction: 360,
                        keep: true,
                        useContainerInlineStyles: false,
                      })}
                    >
                      {skills}
                    </TagCloud>
                  </div>
                </motion.div>

              </AnimatePresence>
            </div>
          </motion.div>
        </div>


      </div>
    </section>
  )
}