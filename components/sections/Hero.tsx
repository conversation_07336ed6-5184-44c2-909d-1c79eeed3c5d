"use client"

import Image from "next/image"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import GitHubIcon from "@mui/icons-material/GitHub"
import LinkedInIcon from "@mui/icons-material/LinkedIn"
import XIcon from "@mui/icons-material/X"
import { TagCloud } from "@frank-mayer/react-tag-cloud"
import { BorderBeam } from "../magicui/border-beam"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "../ui/tooltip"
import WebStoriesIcon from '@mui/icons-material/WebStories';
import EditNoteIcon from '@mui/icons-material/EditNote';
import { Download } from "lucide-react"
import { Button } from "../ui/button"

const socialLinks = [
  {
    icon: GitHubIcon,
    href: "https://github.com/darshanbajgain",
    label: "GitHub",
    color: "text-primary",
  },
  {
    icon: XIcon,
    href: "https://x.com/iamthesun77",
    label: "Twitter",
    color: "text-primary",
  },
  {
    icon: LinkedInIcon,
    href: "https://www.linkedin.com/in/devdarshan10/",
    label: "LinkedIn",
    color: "text-primary",
  },
  {
    icon: EditNoteIcon,
    href: "https://blog.darshanbajgain.com.np/",
    label: "Blog",
    color: "text-primary",
  },
]

const skills = [
  "Next.js",
  "React",
  "JavaScript",
  "TypeScript",
  "Tailwind CSS",
  "Git",
  "REST API",
  "Figma",
  "shadcn/ui",
  "Material UI",
  "Zustand",
  "Jenkins",
  "Vercel",
  "Java",
  "Spring Boot",
  "MySQL",
  "Firebase",
  "Cloudinary",
  "Markdown",
  "Leaflet.js",
  "Axios",
  "WebSocket",
]



export function Hero() {

  return (
    <section className="w-full backdrop-blur-sm py-12 lg:py-20 overflow-hidden relative">
      <div className="w-full min-w-80 max-w-screen-lg mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-8 lg:gap-16">
          {/* Left side: Profile image, name, and social links */}
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="w-full lg:w-2/5 flex flex-col items-center lg:items-center mt-4 mb-6"
          >
            <motion.div
              className="h-32 w-32 sm:h-44 sm:w-44 mb-8 relative group"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="absolute inset-0 rounded-full bg-gradient-to-br from-primary/50 to-secondary/50 blur-lg -z-10 group-hover:blur-xl transition-all duration-300"></div>
              <div className="absolute inset-2 rounded-full bg-gradient-to-br from-primary/30 to-secondary/30 -z-10"></div>
              <div className="absolute inset-0 rounded-full border-2 border-primary/40 group-hover:border-primary/60 transition-all duration-300"></div>
              <Image
                src="/profile-pic(1).png"
                alt="Darshan Bajgain"
                fill
                className="rounded-full object-cover p-1"
                priority
              />
            </motion.div>


            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              className="text-xl sm:text-2xl md:text-3xl leading-tight font-bold text-center mb-6"
            >
              <span className="bg-gradient-to-r from-primary via-primary to-primary/70 bg-clip-text text-transparent">
                Darshan Bajgain
              </span>
            </motion.h1>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="px-4 sm:px-6 py-2 rounded-full bg-gradient-to-r from-primary/15 to-secondary/15 border border-primary/20 text-primary font-semibold text-sm sm:text-base mb-8 shadow-sm"
            >
              Frontend Developer
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="flex items-center justify-center gap-3 sm:gap-4 lg:gap-5 mb-6"
            >
              <TooltipProvider>
                {socialLinks.map((item, index) => (
                  <motion.div
                    key={item.label}
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.7 + index * 0.1, duration: 0.3 }}
                  >
                    <Tooltip key={item.label}>
                      <TooltipTrigger asChild>
                        <Link
                          href={item.href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className={cn(
                            "p-2.5 sm:p-3 rounded-full transition-all duration-300 border border-primary/30",
                            "hover:bg-primary hover:text-white hover:scale-110 hover:border-primary hover:shadow-lg",
                            "bg-card/80 backdrop-blur-sm shadow-md",
                            item.color,
                          )}
                          aria-label={item.label}
                        >
                          <item.icon className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6" />
                        </Link>
                      </TooltipTrigger>

                      <TooltipContent
                        side="bottom"
                        sideOffset={10}
                        className="bg-popover text-popover-foreground px-3 py-1.5 text-sm font-medium shadow-md rounded-lg border-none"
                      >
                        {item.label}
                      </TooltipContent>
                    </Tooltip>
                  </motion.div>
                ))}
              </TooltipProvider>
            </motion.div>

            {/* Portfolio button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2, duration: 0.5 }}
              className="mb-6"
            >

              <Button
                variant="outline"
                size="default"
                className="gap-2 mt-4 border-primary/30 hover:bg-primary hover:text-primary-foreground w-full sm:w-auto transition-all duration-300 shadow-md hover:shadow-lg font-semibold px-6 py-2.5"
                onClick={() => window.open("darshanbajgainresume.pdf", "_blank")}
              >
                <Download className="h-4 w-4" />
                <span>Download Resume</span>
              </Button>
            </motion.div>
          </motion.div>

          {/* Right side: Bio, skills, experience, and education sliders */}
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="flex flex-col items-center lg:items-start w-full lg:w-3/5"
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.5 }}
              className="w-full p-4 sm:p-6 lg:p-8 rounded-2xl bg-gradient-to-br from-card/90 via-card to-primary/10 border border-primary/20 shadow-lg mb-8 relative backdrop-blur-sm"
            >
              <span className="text-xl sm:text-2xl leading-8 tracking-wider font-bold block mb-4 text-primary">👋 Hello there!</span>
              <p className="text-sm sm:text-base lg:text-lg text-foreground/90 leading-relaxed text-left mb-6 font-medium">
                I&apos;m Darshan, a Frontend Developer who loves turning designs into clean, functional code. Passionate
                about building smooth, interactive web experiences.
              </p>
              <div className="flex flex-col sm:flex-row lg:flex-row items-center justify-center lg:justify-start gap-3 sm:gap-4">
                <Link
                  href="/portfolio"
                  className="text-xs sm:text-sm text-primary font-semibold hover:underline flex items-center gap-2 group px-3 sm:px-4 py-2.5 sm:py-3 rounded-xl transition-all duration-300 border border-primary/30
                            hover:bg-primary hover:text-white hover:border-primary hover:shadow-md
                            bg-card/80 backdrop-blur-sm shadow-sm flex-1 sm:flex-none justify-center"
                >
                  <WebStoriesIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span>Portfolio</span>
                </Link>

                <Link
                  href="https://blog.darshanbajgain.com.np/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs sm:text-sm text-primary font-semibold hover:underline flex items-center gap-2 group px-3 sm:px-4 py-2.5 sm:py-3 rounded-xl transition-all duration-300 border border-primary/30
                            hover:bg-primary hover:text-white hover:border-primary hover:shadow-md
                            bg-card/80 backdrop-blur-sm shadow-sm flex-1 sm:flex-none justify-center"
                  onClick={() => {
                    // Update active hash in localStorage to help with navbar selection
                    if (typeof window !== 'undefined') {
                      localStorage.setItem('activeNavItem', '/portfolio');
                    }
                  }}
                >
                  <EditNoteIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span>Blog</span>
                </Link>
              </div>

              {/* Enhanced border beam effect */}
              <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-xl">
                <BorderBeam
                  duration={8}
                  size={600}
                  colorFrom="hsl(var(--primary))"
                  colorTo="hsl(var(--secondary))"
                  className="opacity-30 rounded-xl" />
              </div>
            </motion.div>
            <div className="relative h-[300px] w-full overflow-hidden rounded-xl">

              <AnimatePresence mode="sync">
                <motion.div
                  key="skills"
                  initial={{ opacity: 0, x: 300 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -300 }}
                  transition={{ duration: 0.5 }}
                  className="absolute inset-0 flex flex-col items-center lg:items-start justify-start pt-16 px-4  my-8"
                >

                  <div className="w-full flex items-center justify-center lg:justify-start">
                    <TagCloud
                      className="text-gray-900 dark:text-white text-center font-medium text-sm"
                      options={(w: Window & typeof globalThis) => ({
                        radius: Math.min(150, w.innerWidth / 2, w.innerHeight / 2),
                        maxSpeed: "normal",
                        initSpeed: "slow",
                        direction: 360,
                        keep: true,
                        useContainerInlineStyles: false,
                      })}
                    >
                      {skills}
                    </TagCloud>
                  </div>
                </motion.div>

              </AnimatePresence>
            </div>
          </motion.div>
        </div>


      </div>
    </section>
  )
}