"use client"

import Link from "next/link"
import { ThemeToggle } from "../custom-ui/ThemeToggle"
import { TooltipProvider } from "@/components/ui/tooltip"

export function Navbar() {
  return (
    <TooltipProvider>
      <div className="fixed left-0 right-0 top-0 z-50 flex px-4 py-4 justify-center bg-card">
        <nav className="mt-0 flex items-center justify-between w-full max-w-screen-lg mx-auto px-6 sm:px-8">
          <Link
            href="/"
            className="text-xl font-semibold text-primary transition-colors hover:text-primary/80"
          >
            <PERSON><PERSON> Bajgain
          </Link>
          <ThemeToggle />
        </nav>
      </div>
    </TooltipProvider>
  )
}

