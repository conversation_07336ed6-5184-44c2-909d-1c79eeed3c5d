"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState, useEffect } from "react"
import { ThemeToggle } from "../custom-ui/ThemeToggle"
import { TooltipProvider } from "@/components/ui/tooltip"
import { But<PERSON> } from "@/components/ui/button"
import { Menu, X } from "lucide-react"

const navigationLinks = [
  { name: "Home", href: "#home" },
  { name: "Experience", href: "#experience" },
  { name: "Projects", href: "#projects" },
  { name: "Tools", href: "#tools" },
  { name: "Blogs", href: "#blogs" },
  { name: "Contact", href: "#contact" },
]

export function Navbar() {
  const pathname = usePathname()
  const [activeSection, setActiveSection] = useState("")
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [scrollProgress, setScrollProgress] = useState(0)
  const isHomePage = pathname === "/"

  // Smooth scroll function
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId.replace("#", ""))
    if (element) {
      const navbarHeight = 80 // Approximate navbar height
      const elementPosition = element.offsetTop - navbarHeight
      window.scrollTo({
        top: elementPosition,
        behavior: "smooth"
      })
      // Only set active section when user clicks, not on scroll
      const sectionName = sectionId.replace("#", "")
      setActiveSection(sectionName)

      // Clear active section after 3 seconds
      setTimeout(() => {
        setActiveSection("")
      }, 3000)
    }
    setIsMobileMenuOpen(false)
  }

  // Only track scroll progress, not active sections
  useEffect(() => {
    if (!isHomePage) return

    const handleScroll = () => {
      // Calculate scroll progress
      const documentHeight = document.documentElement.scrollHeight - window.innerHeight
      const progress = (window.scrollY / documentHeight) * 100
      setScrollProgress(Math.min(progress, 100))
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [isHomePage])

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobileMenuOpen && !(event.target as Element).closest('.mobile-menu')) {
        setIsMobileMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [isMobileMenuOpen])

  return (
    <TooltipProvider>
      <div className="fixed left-0 right-0 top-0 z-50 flex px-4 py-4 justify-center bg-card/90 backdrop-blur-md border-b border-primary/10">
        {/* Scroll progress indicator */}
        {isHomePage && (
          <div className="absolute bottom-0 left-0 h-0.5 bg-primary transition-all duration-300"
               style={{ width: `${scrollProgress}%` }} />
        )}

        <nav className="mt-0 flex items-center justify-between w-full max-w-screen-lg mx-auto px-4 sm:px-6 lg:px-8">
          <Link
            href="/"
            className="text-lg sm:text-xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent transition-all hover:from-primary/80 hover:to-primary"
          >
            Darshan Bajgain
          </Link>

          {/* Desktop Navigation - Only show on home page */}
          {isHomePage && (
            <div className="hidden md:flex items-center space-x-1">
              {navigationLinks.map((link) => (
                <button
                  key={link.name}
                  onClick={() => scrollToSection(link.href)}
                  className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    activeSection === link.href.replace("#", "")
                      ? "bg-primary text-primary-foreground shadow-sm"
                      : "text-foreground/80 hover:text-primary hover:bg-primary/10"
                  }`}
                >
                  {link.name}
                </button>
              ))}
              <Link
                href="/portfolio"
                className="ml-4 px-4 py-2 rounded-lg text-sm font-medium bg-primary/10 text-primary hover:bg-primary hover:text-primary-foreground transition-all duration-200 border border-primary/20"
              >
                Portfolio
              </Link>
            </div>
          )}

          {/* Portfolio page navigation */}
          {!isHomePage && (
            <div className="hidden md:flex items-center">
              <Link
                href="/"
                className="px-4 py-2 rounded-lg text-sm font-medium bg-primary/10 text-primary hover:bg-primary hover:text-primary-foreground transition-all duration-200 border border-primary/20"
              >
                Back to Home
              </Link>
            </div>
          )}

          {/* Mobile menu button and theme toggle */}
          <div className="flex items-center gap-2">
            <ThemeToggle />
            {isHomePage && (
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
            )}
          </div>
        </nav>

        {/* Mobile Navigation Menu */}
        {isHomePage && isMobileMenuOpen && (
          <div className="mobile-menu absolute top-full left-0 right-0 bg-card/95 backdrop-blur-md border-b border-primary/10 md:hidden shadow-lg">
            <div className="max-w-screen-lg mx-auto px-4 py-4">
              <div className="flex flex-col space-y-2">
                {navigationLinks.map((link) => (
                  <button
                    key={link.name}
                    onClick={() => scrollToSection(link.href)}
                    className={`px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 text-left ${
                      activeSection === link.href.replace("#", "")
                        ? "bg-primary text-primary-foreground shadow-sm"
                        : "text-foreground/80 hover:text-primary hover:bg-primary/10"
                    }`}
                  >
                    {link.name}
                  </button>
                ))}
                <div className="pt-2 mt-2 border-t border-primary/10">
                  <Link
                    href="/portfolio"
                    className="block px-4 py-3 rounded-lg text-sm font-medium bg-primary/10 text-primary hover:bg-primary hover:text-primary-foreground transition-all duration-200 border border-primary/20 text-center"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Portfolio
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </TooltipProvider>
  )
}

