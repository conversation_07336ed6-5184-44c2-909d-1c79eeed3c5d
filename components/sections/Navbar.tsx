"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState, useEffect } from "react"
import { ThemeToggle } from "../custom-ui/ThemeToggle"
import { TooltipProvider, Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip"
import { Menu, X } from "lucide-react"
import HomeIcon from "@mui/icons-material/Home"
import WorkIcon from "@mui/icons-material/Work"
import CodeIcon from "@mui/icons-material/Code"
// import BuildIcon from "@mui/icons-material/Build"
import WebStoriesIcon from "@mui/icons-material/WebStories"
import EditNoteIcon from '@mui/icons-material/EditNote';
import ContactsIcon from '@mui/icons-material/Contacts';
const navigationLinks = [
  { name: "Home", href: "#home", icon: HomeIcon },
  { name: "Experience", href: "#experience", icon: WorkIcon },
  { name: "Projects", href: "#projects", icon: CodeIcon },
  // { name: "Tools", href: "#tools", icon: BuildIcon },
  { name: "Blogs", href: "#blogs", icon: EditNoteIcon },
  { name: "Contact", href: "#contact", icon: ContactsIcon },
]

export function Navbar() {
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [scrollProgress, setScrollProgress] = useState(0)
  const isHomePage = pathname === "/"

  // Smooth scroll function
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId.replace("#", ""))
    if (element) {
      const navbarHeight = 80 // Approximate navbar height
      const elementPosition = element.offsetTop - navbarHeight
      window.scrollTo({
        top: elementPosition,
        behavior: "smooth"
      })
    }
    setIsMobileMenuOpen(false)
  }

  // Only track scroll progress, not active sections
  useEffect(() => {
    if (!isHomePage) return

    const handleScroll = () => {
      // Calculate scroll progress
      const documentHeight = document.documentElement.scrollHeight - window.innerHeight
      const progress = (window.scrollY / documentHeight) * 100
      setScrollProgress(Math.min(progress, 100))
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [isHomePage])

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobileMenuOpen && !(event.target as Element).closest('.mobile-menu')) {
        setIsMobileMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [isMobileMenuOpen])

  return (
    <TooltipProvider>
      <div className="fixed left-0 right-0 top-0 z-50 flex px-4 py-4 justify-center bg-card/90 backdrop-blur-md border-b border-primary/10">
        {/* Scroll progress indicator */}
        {isHomePage && (
          <div className="absolute bottom-0 left-0 h-0.5 bg-primary transition-all duration-300"
            style={{ width: `${scrollProgress}%` }} />
        )}

        <nav className="mt-0 flex items-center justify-between w-full max-w-screen-lg mx-auto px-4 sm:px-6 lg:px-8">
          <Link
            href="/"
            className="text-lg sm:text-xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent transition-all hover:from-primary/80 hover:to-primary"
          >
            Darshan Bajgain
          </Link>

          {/* Desktop Navigation - Only show on home page */}
          {isHomePage && (
            <div className="hidden md:flex items-center space-x-1">
              {navigationLinks.map((link) => (
                <Tooltip key={link.name}>
                  <TooltipTrigger asChild>
                    <button
                      onClick={() => scrollToSection(link.href)}
                      className="p-2.5 rounded-lg transition-all duration-200 text-foreground/80 hover:text-primary hover:bg-primary/10"
                    >
                      <link.icon className="w-5 h-5" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent className="bg-popover text-popover-foreground px-3 py-1.5 text-sm font-medium shadow-md rounded-lg border-none"
                    side="bottom" sideOffset={8}>
                    <p className="text-sm font-medium">{link.name}</p>
                  </TooltipContent>
                </Tooltip>
              ))}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href="/portfolio"
                    className="ml-3 p-2.5 rounded-lg transition-all duration-200 text-foreground/80 hover:text-primary hover:bg-primary/10"
                  >
                    <WebStoriesIcon className="w-5 h-5" />
                  </Link>
                </TooltipTrigger>
                <TooltipContent className="bg-popover text-popover-foreground px-3 py-1.5 text-sm font-medium shadow-md rounded-lg border-none"
                  side="bottom" sideOffset={8}>
                  <p className="text-sm font-medium">Portfolio</p>
                </TooltipContent>
              </Tooltip>
            </div>
          )}

          {/* Portfolio page navigation */}
          {!isHomePage && (
            <div className="hidden md:flex items-center">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href="/"
                    className="p-2.5 rounded-lg bg-primary/10 text-primary hover:bg-primary hover:text-primary-foreground transition-all duration-200 border border-primary/20"
                  >
                    <HomeIcon className="w-5 h-5" />
                  </Link>
                </TooltipTrigger>
                <TooltipContent className="bg-popover text-popover-foreground px-3 py-1.5 text-sm font-medium shadow-md rounded-lg border-none"
                  side="bottom" sideOffset={8}>
                  <p className="text-sm font-medium">Back to Home</p>
                </TooltipContent>
              </Tooltip>
            </div>
          )}

          {/* Mobile menu button and theme toggle */}
          <div className="flex items-center gap-2">
            <ThemeToggle />
            {isHomePage && (
              <button
                className="md:hidden p-2 rounded-lg text-foreground/80 hover:text-primary hover:bg-primary/10 transition-all duration-200"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </button>
            )}
          </div>
        </nav>

        {/* Mobile Navigation Menu */}
        {isHomePage && isMobileMenuOpen && (
          <div className="mobile-menu absolute top-full left-0 right-0 bg-card/95 backdrop-blur-md border-b border-primary/10 md:hidden shadow-lg">
            <div className="max-w-screen-lg mx-auto px-4 py-4">
              <div className="flex flex-col space-y-2">
                {navigationLinks.map((link) => (
                  <button
                    key={link.name}
                    onClick={() => scrollToSection(link.href)}
                    className="flex items-center gap-3 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 text-left text-foreground/80 hover:text-primary hover:bg-primary/10"
                  >
                    <link.icon className="w-5 h-5" />
                    <span>{link.name}</span>
                  </button>
                ))}
                <div className="pt-2 mt-2 border-t border-primary/10">
                  <Link
                    href="/portfolio"
                    className="flex items-center justify-center gap-2 px-4 py-3 rounded-lg text-sm font-medium text-foreground/80 hover:text-primary hover:bg-primary/10 transition-all duration-200"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <WebStoriesIcon className="w-5 h-5" />
                    <span>Portfolio</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </TooltipProvider>
  )
}

