"use client"

import Link from "next/link"
import { ThemeToggle } from "../custom-ui/ThemeToggle"
import { TooltipProvider } from "@/components/ui/tooltip"

export function Navbar() {
  return (
    <TooltipProvider>
      <div className="fixed left-0 right-0 top-0 z-50 flex px-4 py-4 justify-center bg-card/90 backdrop-blur-md border-b border-primary/10">
        <nav className="mt-0 flex items-center justify-between w-full max-w-screen-lg mx-auto px-4 sm:px-6 lg:px-8">
          <Link
            href="/"
            className="text-lg sm:text-xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent transition-all hover:from-primary/80 hover:to-primary"
          >
            <PERSON>shan Bajgain
          </Link>
          <ThemeToggle />
        </nav>
      </div>
    </TooltipProvider>
  )
}

