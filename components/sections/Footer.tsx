"use client"

import Link from "next/link"

export default function Footer() {
  return (
    <footer className="bg-card/80 backdrop-blur-sm border-t border-primary/10">
      <div className="max-w-screen-lg mx-auto px-6 sm:px-8">
        <div className="flex flex-col items-center py-10 space-y-8">
          <div className="flex flex-col items-center">
            <Link
              href="/"
              className="text-2xl font-semibold tracking-tight text-primary hover:text-primary/90 transition-colors"
            >
              Darshan Bajgain
            </Link>
            <div className="h-1 w-12 bg-primary/20 rounded-full mt-2"></div>
          </div>

          <div className="text-center pt-6 border-t border-primary/10 w-full">
            <p className="text-sm text-muted-foreground mb-1">
              Made using Next.js, Tailwind CSS, and shadcn/ui
            </p>
            <p className="text-sm text-muted-foreground">
              &copy; {new Date().getFullYear()} Darshan Bajgain. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}