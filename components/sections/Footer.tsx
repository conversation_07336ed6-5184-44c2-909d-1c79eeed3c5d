"use client"

import Link from "next/link"

export default function Footer() {
  return (
    <footer className="bg-card/90 backdrop-blur-md border-t border-primary/20 mt-16">
      <div className="max-w-screen-lg mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center py-12 space-y-8">
          <div className="flex flex-col items-center">
            <Link
              href="/"
              className="text-xl sm:text-2xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent hover:from-primary/80 hover:to-primary transition-all"
            >
              Darshan Bajgain
            </Link>
            <div className="h-1 w-16 bg-gradient-to-r from-primary to-primary/60 rounded-full mt-3"></div>
          </div>

          <div className="text-center pt-8 border-t border-primary/20 w-full">
            <p className="text-sm text-foreground/70 mb-2 font-medium">
              Made using Next.js, Tailwind CSS, and shadcn/ui
            </p>
            <p className="text-sm text-foreground/60">
              &copy; {new Date().getFullYear()} Darshan Bajgain. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}