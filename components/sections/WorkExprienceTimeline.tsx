"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Briefcase, GraduationCap, Clock, MapPin, ChevronDown } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { BorderBeam } from "../magicui/border-beam"
import { SectionWrapper } from "@/components/ui/section-wrapper"

interface TimelineItem {
  id: string
  title: string
  organization: string
  location: string
  date: string
  description: string[]
  skills: {
    text: string
    category: "frontend" | "backend" | "language" | "tool" | "soft" | "database" | "cloud"
  }[]
  icon: "education" | "work"
  grade?: string
}

const timelineData: TimelineItem[] = [
  {
    id: "current-job",
    title: "Frontend Web Engineer",
    organization: "Paragon Global LLC",
    location: "US (Remote)",
    date: "Aug 2024 - May 2025",
    description: [
      "Developed the frontend for Opplaus, a job tracking platform with LinkedIn, Monster, and Indeed integration.",
      "Implemented job-saving features in a browser extension with real-time syncing.",
      "Designed an interactive job tracking table with inline editing and status updates.",
      "Built analytics dashboards using Recharts with live WebSocket updates.",
      "Developed an interactive map with Leaflet.js for job location visualization.",
      "Optimized API integration with Axios and managed global state using Zustand.",
      "Enhanced UI responsiveness and accessibility with Tailwind CSS and dark/light mode.",
      "Migrated the project from CRA to Vite for faster builds and performance.",
      "Developed a Kanban-style job tracking board to manage job applications efficiently, featuring drag-and-drop functionality."
    ],
    skills: [
      { text: "React", category: "frontend" },
      { text: "Tailwind CSS", category: "frontend" },
      { text: "Zustand", category: "tool" },
      { text: "REST APIs", category: "tool" },
      { text: "WebSockets", category: "tool" },
      { text: "Leaflet.js", category: "frontend" },
      { text: "Axios", category: "tool" },
      { text: "Git", category: "tool" },
      { text: "Agile", category: "soft" },
    ],
    icon: "work",
  },
  {
    id: "internship",
    title: "Frontend Web Developer Intern",
    organization: "Truenary Solutions Pvt. Ltd.",
    location: "Kathmandu, Nepal",
    date: "May 2024 - July 2024",
    description: [
      "Developed a responsive e-commerce landing page using React and Tailwind CSS.",
      "Implemented reusable UI components for scalable frontend development.",
      "Built an interactive landing page for the Saino dating app.",
      "Worked closely with senior developers to implement features and resolve issues.",
    ],
    skills: [
      { text: "React", category: "frontend" },
      { text: "JavaScript", category: "language" },
      { text: "Tailwind CSS", category: "frontend" },
      { text: "HTML5", category: "frontend" },
      { text: "CSS3", category: "frontend" },
      { text: "Git", category: "tool" },
    ],
    icon: "work",
  },
  {
    id: "education",
    title: "BSc Computer Science and Information Technology",
    organization: "Tribhuvan University - IOST",
    location: "Kathmandu, Nepal",
    date: "2019 - 2024",
    description: [
      "Major in Computer Science with a focus on software development.",
      "Relevant coursework: Data Structures, Algorithms, Web Technologies, Database Management.",
      "Final year project: Built a Accidented Car Detection System using CNN Algorithm.",
    ],
    skills: [
      { text: "Java", category: "language" },
      { text: "JavaScript", category: "language" },
      { text: "Web Development", category: "frontend" },
      { text: "MySQL", category: "database" },
      { text: "C/C++", category: "language" },
      { text: "OOP", category: "soft" },
      { text: "SEO", category: "soft" },
    ],
    icon: "education",
    grade: "Division: First",
  },
];

const getBadgeColor = (category: TimelineItem["skills"][0]["category"]) => {
  switch (category) {
    case "frontend":
      return "bg-blue-500 text-white"
    case "backend":
      return "bg-green-500 text-white"
    case "language":
      return "bg-purple-500 text-white"
    case "tool":
      return "bg-orange-500 text-white"
    case "soft":
      return "bg-pink-500 text-white"
    case "database":
      return "bg-yellow-500 text-white"
    case "cloud":
      return "bg-cyan-500 text-white"
    default:
      return "bg-gray-500 text-white"
  }
}

export default function WorkExperienceTimeline() {
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const toggleItem = (id: string) => {
    setExpandedItems((prev) => (prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]))
  }

  return (
    <SectionWrapper>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-2xl sm:text-3xl font-medium text-primary relative inline-block"
        >
          Experience & Qualification
          <span className="absolute -bottom-2 left-0 w-12 h-1 bg-primary rounded-full"></span>
        </motion.h2>
      
      </div>
      <div className="relative">
        <div className="absolute left-[27px] top-3 h-[calc(100%-2rem)] w-0.5 bg-primary/20 md:left-[31px]" />
        {timelineData.map((item, index) => (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className="mb-8 last:mb-0"
          >
            <div className="flex gap-4 md:gap-6">
              <motion.div
                className="relative z-10 mt-2"
                whileHover={{ scale: 1.1 }}
                transition={{ duration: 0.2 }}
              >
                <div className="w-14 h-14 rounded-full bg-primary flex items-center justify-center shadow-md">
                  {item.icon === "education" ? (
                    <GraduationCap className="w-7 h-7 text-primary-foreground" />
                  ) : (
                    <Briefcase className="w-7 h-7 text-primary-foreground" />
                  )}
                </div>
              </motion.div>
              <Card className="flex-1 p-6 group overflow-hidden border border-primary/10 shadow-sm bg-background hover:bg-accent/50 transition-all duration-300 ease-in-out">
                <div className="flex flex-col md:flex-row md:items-start justify-between gap-2">
                  <div>
                    <h3 className="text-lg font-semibold group-hover:text-primary text-foreground">{item.title}</h3>
                    <p className="text-muted-foreground">{item.organization}</p>
                    {item.grade && <p className="text-sm text-muted-foreground font-medium mt-1">{item.grade}</p>}
                  </div>
                  <div className="flex flex-col gap-1 text-sm text-muted-foreground md:text-right">
                    <div className="flex items-center gap-2 md:justify-end">
                      <div className="p-1 bg-primary/10 rounded-full">
                        <Clock className="h-3.5 w-3.5 text-primary" />
                      </div>
                      <span>{item.date}</span>
                    </div>
                    <div className="flex items-center gap-2 md:justify-end">
                      <div className="p-1 bg-primary/10 rounded-full">
                        <MapPin className="h-3.5 w-3.5 text-primary" />
                      </div>
                      <span>{item.location}</span>
                    </div>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="max-w-xs justify-start py-2 mt-4 text-muted-foreground hover:text-primary relative"
                  onClick={() => toggleItem(item.id)}
                >
                  <span>{expandedItems.includes(item.id) ? "Less Details" : "More Details"}</span>
                  <ChevronDown
                    className={`h-4 w-4 ml-2 transition-transform duration-300 ${expandedItems.includes(item.id) ? "rotate-180" : ""
                      }`}
                  />
                  <BorderBeam
                    duration={10}
                    reverse={true}
                    size={200}
                    colorFrom="hsl(var(--primary))"
                    colorTo="transparent"
                    className="opacity-30"
                  />
                </Button>
                <AnimatePresence>
                  {expandedItems.includes(item.id) && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="p-4 mt-2 bg-primary/5 rounded-lg border border-primary/10">
                        <ul className="space-y-2 mb-4 text-sm text-foreground">
                          {item.description.map((desc, i) => (
                            <li key={i} className="flex items-start gap-2">
                              <span className="bg-primary rounded-full h-1.5 w-1.5 mt-1.5 flex-shrink-0"></span>
                              <span>{desc}</span>
                            </li>
                          ))}
                        </ul>
                        <div className="flex flex-wrap gap-2">
                          {item.skills.map((skill) => (
                            <Badge
                              key={skill.text}
                              className={`${getBadgeColor(skill.category)} px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 cursor-default hover:shadow-md`}
                            >
                              {skill.text}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </Card>
            </div>
          </motion.div>
        ))}
      </div>
    </SectionWrapper>
  )
}

