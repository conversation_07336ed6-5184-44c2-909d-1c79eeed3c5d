"use client"

import { ArrowUpRight } from "lucide-react"
import { motion } from "framer-motion"
import { SectionWrapper } from "@/components/ui/section-wrapper"

const blogPosts = [

  {
    title: "🚀Setting Up (2025) Next.js 15 with ShadCN & Tailwind CSS v4 (No Config Needed) + Dark Mode",
    excerpt:
      "With the release of Next.js 15 and Tailwind CSS v4, setting up a modern web project has never been easier.",
    date: "Jan 28, 2025",
    readTime: "10 min read",
    href: "https://dev.to/darshan_bajgain/setting-up-2025-nextjs-15-with-shadcn-tailwind-css-v4-no-config-needed-dark-mode-5kl",
  },

  {
    title: "Troubleshooting Rendering Issues with Tailwind in React App",
    excerpt:
      "Are you encountering issues with rendering Markdown content in your React app that uses Tailwind CSS? Markdown is a popular choice for creating formatted text, but it can sometimes clash with the styling provided by Tailwind CSS. In this blog post, I will mention the problem and also provide solutions to ensure your Markdown content renders beautifully in your React application.",
    date: "OCt 15, 2023",
    readTime: "5 min read",
    href: "https://blog.darshanbajgain.com.np/posts/troublshoot-markdown-rendreing-issue-tailwind",
  },
  {
    title: "Setting Up ConvertKit for Email Subscriptions in Your Next.js Blog",
    excerpt:
      "Email newsletters are one of the most effective ways to connect with your audience and build a loyal following. In this guide, I'll walk you through the process of setting up ConvertKit with your Next.js blog to collect email subscriptions.",
    date: "May 6, 2025",
    readTime: "6 min read",
    href: "https://blog.darshanbajgain.com.np/posts/setting-up-convertkit-for-email-subscriptions",
  },


]

const BlogCard = ({ post }: { post: (typeof blogPosts)[0] }) => {
  return (
    <motion.div
      whileHover={{ scale: 1.01 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.5,
        hover: { duration: 0.2 }
      }}
    >
      <a href={post.href} target="_blank" rel="noopener noreferrer" className="block">
        <div className="group rounded-xl bg-background hover:bg-accent/50 p-6 transition-colors duration-300
          border border-primary/10 shadow-sm hover:shadow-md">
          <div className="flex justify-between items-start gap-4">
            <div className="space-y-3">
              <div className="flex items-start gap-2">
                <h3 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors duration-300">
                  {post.title}
                </h3>
                <div className="bg-primary/10 rounded-full p-1.5 group-hover:bg-primary/20 transition-colors">
                  <ArrowUpRight className="h-5 w-5 text-primary transition-all duration-300 flex-shrink-0" />
                </div>
              </div>
              <p className="text-muted-foreground line-clamp-2 text-sm">{post.excerpt}</p>
              <div className="flex items-center gap-3 text-xs text-muted-foreground">
                <span className="px-2 py-1 bg-primary/5 rounded-full">{post.date}</span>
                <span>•</span>
                <span className="px-2 py-1 bg-secondary/5 rounded-full">{post.readTime}</span>
              </div>
            </div>
          </div>
        </div>
      </a>
    </motion.div>
  )
}

export default function Blogs() {
  return (
    <SectionWrapper title="Featured Blog Posts">
      <div className="space-y-4">
        {blogPosts.map((post) => (
          <BlogCard key={post.title} post={post} />
        ))}
      </div>
    </SectionWrapper>
  )
}

