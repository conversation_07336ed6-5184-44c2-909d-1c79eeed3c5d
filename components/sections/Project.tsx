"use client"

import Image from "next/image"
import Link from "next/link"
import { ExternalLink, ArrowRight, Eye } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { SectionWrapper } from "@/components/ui/section-wrapper"
import { motion } from "framer-motion"
import { Project, projects } from "@/data/projects"
import { Badge } from "@/components/ui/badge"

const FeaturedProjectCard = ({ project, index }: { project: Project, index: number }) => {
  const isEven = index % 2 === 0;

  return (
    <motion.div
      initial={{ opacity: 0, x: isEven ? -50 : 50 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.8, delay: index * 0.2 }}
      className={`group relative flex flex-col lg:flex-row gap-8 items-center ${
        isEven ? 'lg:flex-row' : 'lg:flex-row-reverse'
      }`}
    >
      {/* Image Section */}
      <div className="relative w-full lg:w-1/2">
        <div className="relative aspect-[4/3] overflow-hidden rounded-3xl bg-gradient-to-br from-primary/10 to-secondary/10 shadow-2xl group-hover:shadow-3xl transition-all duration-700">
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5" />

          {/* Main image */}
          <Image
            src={project.image || "/placeholder.svg"}
            alt={project.title}
            fill
            className="object-cover transition-transform duration-700 group-hover:scale-105"
          />

          {/* Overlay gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

          {/* Floating demo button */}
          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500">
            <Link
              href={project.demoUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-3 px-6 py-3 bg-white/95 backdrop-blur-sm rounded-full shadow-xl hover:bg-white hover:scale-105 transition-all duration-300 group/btn"
            >
              <Eye className="w-5 h-5 text-primary" />
              <span className="font-semibold text-gray-900">View Live Demo</span>
              <ExternalLink className="w-4 h-4 text-primary" />
            </Link>
          </div>

          {/* Project number badge */}
          <div className="absolute top-6 left-6">
            <div className="w-12 h-12 bg-primary/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-lg">{index + 1}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="w-full lg:w-1/2 space-y-6">
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="h-1 w-12 bg-gradient-to-r from-primary to-secondary rounded-full" />
            <span className="text-sm font-medium text-primary uppercase tracking-wider">Featured Project</span>
          </div>

          <h3 className="text-3xl lg:text-4xl font-bold text-foreground leading-tight group-hover:text-primary transition-colors duration-300">
            {project.title}
          </h3>

          <p className="text-lg text-muted-foreground leading-relaxed">
            {project.description}
          </p>
        </div>

        {/* Technology stack - limit to 3 */}
        {project.tags && (
          <div className="flex flex-wrap gap-3">
            {project.tags.slice(0, 3).map((tag: string) => (
              <Badge
                key={tag}
                className="px-4 py-2 bg-primary/10 text-primary border border-primary/20 hover:bg-primary/20 transition-all duration-300 font-medium"
              >
                {tag}
              </Badge>
            ))}
          </div>
        )}

        {/* Action buttons */}
        <div className="flex flex-col sm:flex-row gap-4 pt-4">
          <Button
            asChild
            size="lg"
            className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 group/btn"
          >
            <Link
              href={project.demoUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center gap-2"
            >
              <Eye className="w-5 h-5" />
              <span>Live Demo</span>
              <ArrowRight className="w-4 h-4 transition-transform group-hover/btn:translate-x-1" />
            </Link>
          </Button>

          {project.githubUrl && (
            <Button
              asChild
              variant="outline"
              size="lg"
              className="border-primary/30 hover:bg-primary/10 hover:border-primary/50 transition-all duration-300"
            >
              <Link
                href={project.githubUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center gap-2"
              >
                <span>View Code</span>
                <ExternalLink className="w-4 h-4" />
              </Link>
            </Button>
          )}
        </div>
      </div>
    </motion.div>
  )
}

export default function ProjectsSection() {
  // Display only the first 3 projects in the main page
  const featuredProjects = projects.slice(0, 3);

  return (
    <SectionWrapper title="Featured Projects">
      <div className="space-y-16 lg:space-y-24">
        {/* Featured projects with alternating layout */}
        {featuredProjects.map((project, index) => (
          <FeaturedProjectCard key={project.id} project={project} index={index} />
        ))}

        {/* View all projects section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center pt-8"
        >
          <div className="space-y-6">
            <div className="space-y-3">
              <h3 className="text-2xl font-bold text-foreground">Want to see more?</h3>
              <p className="text-muted-foreground max-w-md mx-auto">
                Explore my complete portfolio with detailed project breakdowns, source code, and live demonstrations.
              </p>
            </div>

            <Button
              asChild
              size="lg"
              className="group bg-gradient-to-r from-primary via-primary to-secondary hover:from-primary/90 hover:via-primary/90 hover:to-secondary/90 text-primary-foreground shadow-xl hover:shadow-2xl transition-all duration-500 px-8 py-3"
            >
              <Link href="/portfolio" className="flex items-center gap-3">
                <span className="font-semibold">View Complete Portfolio</span>
                <ArrowRight className="h-5 w-5 transition-transform group-hover:translate-x-2" />
              </Link>
            </Button>
          </div>
        </motion.div>
      </div>
    </SectionWrapper>
  )
}

