"use client"

import Image from "next/image"
import Link from "next/link"
import { ExternalLink, ArrowRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { SectionWrapper } from "@/components/ui/section-wrapper"
import { motion } from "framer-motion"
import { projects } from "@/data/projects"

export default function ProjectsSection() {
  // Display only the first 3 projects in the main page
  const featuredProjects = projects.slice(0, 3);

  return (
    <SectionWrapper title="Projects">
      <div className="flex flex-col space-y-6">
        {featuredProjects.map((project, index) => (
          <motion.div
            key={project.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className="group relative bg-background/40 backdrop-blur-sm hover:bg-accent/30 transition-all duration-300 rounded-xl overflow-hidden
              border border-primary/20 shadow-md hover:shadow-lg"
          >
            <div className="flex flex-col lg:flex-row gap-6 p-6">
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-5 w-full">
                <div className="relative h-[140px] w-full sm:h-40 sm:w-40 flex-shrink-0 rounded-lg overflow-hidden bg-muted/50
                  border border-primary/20 group-hover:border-primary/40 transition-colors duration-300">
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-secondary/10 z-0"></div>
                  <Image
                    src={project.image || "/placeholder.svg"}
                    alt={project.title}
                    fill
                    className="object-cover z-10 transition-transform duration-500 group-hover:scale-105"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-xl font-semibold text-foreground mb-3 group-hover:text-primary transition-colors duration-300">
                    {project.title}
                  </h3>
                  <p className="text-sm text-muted-foreground leading-relaxed line-clamp-2">{project.description}</p>

                  {project.tags && (
                    <div className="flex flex-wrap gap-2 mt-3">
                      {project.tags.slice(0, 5).map(tag => (
                        <span key={tag} className="text-xs px-2.5 py-1 bg-primary/20 text-primary rounded-full font-medium">
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-3 justify-start sm:justify-end lg:min-w-[180px]">
                <Button
                  asChild
                  variant="outline"
                  size="sm"
                  className="flex-1 sm:flex-none border-primary/30 hover:bg-primary hover:text-primary-foreground transition-colors duration-300"
                >
                  <Link
                    href={project.demoUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 justify-center"
                  >
                    <ExternalLink className="h-4 w-4" />
                    <span className="inline">Live Demo</span>
                  </Link>
                </Button>
              </div>
            </div>
          </motion.div>
        ))}

        <div className="flex justify-center mt-4">
          <Button asChild className="group">
            <Link href="/portfolio" className="flex items-center gap-2">
              View All Projects
              <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </div>
      </div>
    </SectionWrapper>
  )
}

