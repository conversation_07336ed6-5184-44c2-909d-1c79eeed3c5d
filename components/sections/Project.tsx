"use client"

import Image from "next/image"
import Link from "next/link"
import { ExternalLink, ArrowRight, Eye, Sparkles } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { SectionWrapper } from "@/components/ui/section-wrapper"
import { motion } from "framer-motion"
import { projects } from "@/data/projects"
import { Badge } from "@/components/ui/badge"

const ProjectCard = ({ project, index }: { project: any, index: number }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.15 }}
      className="group relative"
    >
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-card/90 via-card to-primary/5 border border-primary/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 hover:rotate-1">
        {/* Background gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

        {/* Subtle sparkle effect */}
        <div className="absolute top-4 left-4 w-2 h-2 bg-primary/30 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-700 delay-200 animate-pulse" />
        <div className="absolute top-8 right-8 w-1 h-1 bg-secondary/40 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-700 delay-300 animate-pulse" />

        {/* Image section */}
        <div className="relative h-48 sm:h-56 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent z-10" />
          <Image
            src={project.image || "/placeholder.svg"}
            alt={project.title}
            fill
            className="object-cover transition-transform duration-700 group-hover:scale-110"
          />

          {/* Floating action button */}
          <div className="absolute top-4 right-4 z-20">
            <Link
              href={project.demoUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 group/btn"
            >
              <ExternalLink className="w-4 h-4 text-primary group-hover/btn:text-primary/80" />
            </Link>
          </div>
        </div>

        {/* Content section */}
        <div className="p-6 space-y-4">
          <div className="space-y-2">
            <h3 className="text-xl font-bold text-foreground group-hover:text-primary transition-colors duration-300 line-clamp-2">
              {project.title}
            </h3>
            <p className="text-sm text-muted-foreground leading-relaxed line-clamp-3">
              {project.description}
            </p>
          </div>

          {/* Tags */}
          {project.tags && (
            <div className="flex flex-wrap gap-2">
              {project.tags.slice(0, 4).map((tag: string) => (
                <Badge
                  key={tag}
                  variant="secondary"
                  className="text-xs px-2.5 py-1 bg-primary/10 text-primary border border-primary/20 hover:bg-primary/20 transition-colors duration-200"
                >
                  {tag}
                </Badge>
              ))}
              {project.tags.length > 4 && (
                <Badge variant="outline" className="text-xs px-2.5 py-1 text-muted-foreground">
                  +{project.tags.length - 4}
                </Badge>
              )}
            </div>
          )}

          {/* Action button */}
          <div className="pt-2">
            <Button
              asChild
              className="w-full bg-primary/10 text-primary hover:bg-primary hover:text-primary-foreground border border-primary/30 hover:border-primary transition-all duration-300 group/btn"
              variant="outline"
            >
              <Link
                href={project.demoUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center gap-2"
              >
                <Eye className="w-4 h-4" />
                <span>View Project</span>
                <ArrowRight className="w-4 h-4 transition-transform group-hover/btn:translate-x-1" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default function ProjectsSection() {
  // Display only the first 3 projects in the main page
  const featuredProjects = projects.slice(0, 3);

  return (
    <SectionWrapper title="Featured Projects">
      <div className="space-y-8">
        {/* Projects grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
          {featuredProjects.map((project, index) => (
            <ProjectCard key={project.id} project={project} index={index} />
          ))}
        </div>

        {/* View all projects button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="flex justify-center pt-4"
        >
          <Button
            asChild
            size="lg"
            className="group bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <Link href="/portfolio" className="flex items-center gap-2">
              <span>Explore All Projects</span>
              <ArrowRight className="h-5 w-5 transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </motion.div>
      </div>
    </SectionWrapper>
  )
}

