export default function StructuredData() {
  const personSchema = {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "<PERSON><PERSON> Bajgain",
    "jobTitle": "Frontend Developer",
    "description": "Experienced Frontend Developer specializing in React, Next.js, TypeScript, and modern web technologies",
    "url": "https://darshanbajgain.com.np",
    "image": "https://darshanbajgain.com.np/profile-pic.png",
    "sameAs": [
      "https://github.com/darshanbajgain",
      "https://linkedin.com/in/darshan-bajgain",
      "https://blog.darshanbajgain.com.np"
    ],
    "worksFor": {
      "@type": "Organization",
      "name": "Paragon Global LLC"
    },
    "knowsAbout": [
      "React",
      "Next.js", 
      "TypeScript",
      "JavaScript",
      "Frontend Development",
      "Web Development",
      "UI/UX Design",
      "Responsive Design"
    ],
    "alumniOf": {
      "@type": "EducationalOrganization",
      "name": "Tribhuvan University"
    }
  }

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Darshan Bajgain Portfolio",
    "description": "Frontend Developer Portfolio showcasing React, Next.js, and TypeScript projects",
    "url": "https://darshanbajgain.com.np",
    "author": {
      "@type": "Person",
      "name": "Darshan Bajgain"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://darshanbajgain.com.np/portfolio?search={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  }

  const professionalServiceSchema = {
    "@context": "https://schema.org",
    "@type": "ProfessionalService",
    "name": "Darshan Bajgain - Frontend Development Services",
    "description": "Professional frontend development services specializing in React, Next.js, and modern web technologies",
    "provider": {
      "@type": "Person",
      "name": "Darshan Bajgain"
    },
    "areaServed": "Worldwide",
    "serviceType": "Frontend Development",
    "url": "https://darshanbajgain.com.np"
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(personSchema) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(websiteSchema) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(professionalServiceSchema) }}
      />
    </>
  )
}
