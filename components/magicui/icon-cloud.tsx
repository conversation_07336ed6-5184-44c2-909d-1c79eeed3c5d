"use client"

import * as React from "react"
import { useTheme } from "next-themes"

import { cn } from "@/lib/utils"

interface IconCloudProps extends React.HTMLAttributes<HTMLDivElement> {
  icons: React.ReactNode[]
  randomize?: boolean
}

export function IconCloud({ icons, randomize = true, className, ...props }: IconCloudProps) {
  const { theme } = useTheme()
  const [mounted, setMounted] = React.useState(false)
  const cloudRef = React.useRef<HTMLDivElement>(null)
  const [radius, setRadius] = React.useState(150)
  const rotationSpeed = 0.001 // Adjust this value to control rotation speed

  React.useEffect(() => {
    setMounted(true)
    if (cloudRef.current) {
      const { width, height } = cloudRef.current.getBoundingClientRect()
      setRadius(Math.min(width, height) / 2)
    }
  }, [])

  React.useEffect(() => {
    if (!mounted) return

    let frame: number
    let sphere: {
      vector: { x: number; y: number; z: number }
      element: HTMLDivElement
      phi: number
      theta: number
    }[] = []
    let mouseX = window.innerWidth / 2
    let mouseY = window.innerHeight / 2
    let isMobile = window.matchMedia("(max-width: 768px)").matches

    const init = () => {
      const cloud = cloudRef.current
      if (!cloud) return

      const iconElements = Array.from(cloud.querySelectorAll(".icon-item")) as HTMLDivElement[]

      sphere = iconElements.map((element, i) => {
        const phi = Math.acos(-1 + (2 * i) / iconElements.length)
        const theta = Math.sqrt(iconElements.length * Math.PI) * phi

        return {
          element,
          phi,
          theta,
          vector: {
            x: Math.cos(theta) * Math.sin(phi),
            y: Math.sin(theta) * Math.sin(phi),
            z: Math.cos(phi),
          },
        }
      })

      if (randomize) {
        sphere = sphere.sort(() => Math.random() - 0.5)
      }

      update()
    }

    const update = () => {
      const time = Date.now() * rotationSpeed
      sphere.forEach(({ element, vector }) => {
        // Add automatic rotation
        const rx = isMobile ? time : (mouseX / window.innerWidth) * Math.PI + time
        const ry = isMobile ? time : (mouseY / window.innerHeight) * Math.PI + time
        const ny = Math.cos(rx) * vector.y - Math.sin(rx) * vector.z
        const nz = Math.sin(rx) * vector.y + Math.cos(rx) * vector.z
        const nx = Math.cos(ry) * vector.x - Math.sin(ry) * nz
        const nzUpdated = Math.sin(ry) * vector.x + Math.cos(ry) * nz

        const scale = (nzUpdated + 2) / 3
        const opacity = (nzUpdated + 1.5) / 2.5

        element.style.transform = `translate(-50%, -50%) scale(${scale})`
        element.style.opacity = `${opacity}`
        element.style.zIndex = `${Math.floor(opacity * 100)}`
        element.style.left = `${nx * radius + radius}px`
        element.style.top = `${ny * radius + radius}px`
      })

      frame = requestAnimationFrame(update)
    }

    const handleMouseMove = (e: MouseEvent) => {
      mouseX = e.clientX
      mouseY = e.clientY
    }

    const handleResize = () => {
      if (cloudRef.current) {
        const { width, height } = cloudRef.current.getBoundingClientRect()
        setRadius(Math.min(width, height) / 2)
      }
      isMobile = window.matchMedia("(max-width: 768px)").matches
    }

    window.addEventListener("mousemove", handleMouseMove)
    window.addEventListener("resize", handleResize)

    init()

    return () => {
      cancelAnimationFrame(frame)
      window.removeEventListener("mousemove", handleMouseMove)
      window.removeEventListener("resize", handleResize)
    }
  }, [mounted, radius, randomize])

  if (!mounted) {
    return null
  }

  return (
    <div ref={cloudRef} className={cn("relative size-full overflow-hidden", className)} {...props}>
      {icons.map((icon, i) => (
        <div
          key={i}
          className={cn(
            "icon-item absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform transition-transform",
            theme === "dark" ? "text-white" : "text-black",
          )}
        >
          {icon}
        </div>
      ))}
    </div>
  )
}