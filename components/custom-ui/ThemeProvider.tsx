// "use client"

// import { useThemeStore } from "@/lib/store"
// import { useEffect } from "react"
// import type React from "react" // Added import for React

// export function ThemeProvider({ children }: { children: React.ReactNode }) {
//     const { theme } = useThemeStore()

//     useEffect(() => {
//         const root = window.document.documentElement
//         root.classList.remove("light", "dark")
//         root.classList.add(theme)
//     }, [theme])

//     return <>{children}</>
// }


//usign next js theme recommended approach
"use client"

import { ThemeProvider as NextThemesProvider } from "next-themes"

// ✅ `React.ReactNode` is a type for children props (anything inside JSX)
interface ThemeProviderProps {
    children: React.ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
    return (
        <NextThemesProvider attribute="class" defaultTheme="system" enableSystem={true}>
            {children}
        </NextThemesProvider>
    )
}
