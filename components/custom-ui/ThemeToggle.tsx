// "use client"

// import { Moon, Sun } from "lucide-react"
// import { But<PERSON> } from "@/components/ui/button"
// import { Tooltip, TooltipContent, Toolt<PERSON>Trigger, TooltipProvider } from "@/components/ui/tooltip"
// import { useThemeStore } from "@/lib/store"

// export function ThemeToggle() {
//     const { theme, toggleTheme } = useThemeStore()

//     return (
//         <TooltipProvider>
//             <Tooltip>
//                 <TooltipTrigger asChild>
//                     <Button
//                         variant="ghost"
//                         size="icon"
//                         onClick={toggleTheme}
//                         className="h-12 w-12 rounded-full transition-colors duration-200 hover:bg-accent hover:text-accent-foreground"
//                     >
//                         <Sun className="h-6 w-6 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
//                         <Moon className="absolute h-6 w-6 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
//                         <span className="sr-only">Toggle theme</span>
//                     </Button>
//                 </TooltipTrigger>
//                 <TooltipContent>
//                     <p>{theme === "light" ? "Dark" : "Light"} mode</p>
//                 </TooltipContent>
//             </Tooltip>
//         </TooltipProvider>
//     )
// }


"use client"

import { useTheme } from "next-themes"
import { Button } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip"
import { useEffect, useState } from "react"
import DarkModeIcon from '@mui/icons-material/DarkMode';
import LightModeIcon from '@mui/icons-material/LightMode';

export function ThemeToggle() {
    // ✅ Fetches current theme & `setTheme` function
    const { theme, setTheme, systemTheme } = useTheme()
    const [mounted, setMounted] = useState(false)

    // ✅ Fixes hydration issue (Ensures correct theme is applied after mount)
    useEffect(() => {
        setMounted(true)
    }, [])

    // ✅ Use system theme if theme is "system"
    const currentTheme = theme === "system" ? systemTheme : theme

    if (!mounted) return null // Prevents mismatched UI during hydration

    return (
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger asChild>
                    <Button
                        variant="outline"
                        size="icon"
                        onClick={() => setTheme(theme === "light" ? "dark" : "light")}
                        className="h-10 w-10 rounded-full transition-all duration-300 border-primary/20 hover:bg-primary hover:text-primary-foreground"
                    >
                        <LightModeIcon className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
                        <DarkModeIcon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
                    </Button>
                </TooltipTrigger>
                <TooltipContent
                    side="bottom"
                    sideOffset={10}
                    className="bg-popover text-popover-foreground px-3 py-1.5 text-sm font-medium shadow-md rounded-lg border-none"
                >
                    {currentTheme === "light" ? "Dark" : "Light"} mode
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    )
}