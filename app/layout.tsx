import { ThemeProvider } from "@/components/custom-ui/ThemeProvider"
import type { Metadata } from "next"
import "./globals.css"
import type React from "react"
import { Navbar } from "@/components/sections/Navbar"
import Footer from "@/components/sections/Footer"
import { Poppins } from 'next/font/google';
import { Toaster } from "@/components/ui/toaster";

const poppinsFont = Poppins({
  weight: '400',
  subsets: ['latin'],
  variable: '--font-poppins',
});

export const metadata: Metadata = {
  title: "<PERSON><PERSON> Bajgain - Frontend Engineer",
  description: "Creative Web Designer crafting visually stunning websites",
  icons: {
    icon: [
      {
        url: '/favicon.ico',
        sizes: 'any',
      },
    ],
    shortcut: '/favicon.ico',
    apple: '/favicon.ico',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={poppinsFont.className}>
        <ThemeProvider>
          <div className="min-h-screen bg-gradient-to-br from-background via-card/50 to-primary/5 relative">
            {/* Global background decoration */}
            <div className="fixed inset-0 bg-grid-pattern opacity-5 dark:opacity-10 pointer-events-none"></div>
            <div className="fixed top-0 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl -z-10"></div>
            <div className="fixed bottom-0 right-1/4 w-96 h-96 bg-secondary/10 rounded-full blur-3xl -z-10"></div>
            <div className="fixed top-1/2 right-0 w-64 h-64 bg-primary/5 rounded-full blur-2xl -z-10"></div>

            <div className="relative z-10">
              <Navbar />
              {children}
              <Footer />
              <Toaster />
            </div>
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}