import { ThemeProvider } from "@/components/custom-ui/ThemeProvider"
import type { Metadata } from "next"
import "./globals.css"
import type React from "react"
import { Navbar } from "@/components/sections/Navbar"
import Footer from "@/components/sections/Footer"
import { Poppins } from 'next/font/google';
import { Toaster } from "@/components/ui/toaster";

const poppinsFont = Poppins({
  weight: '400',
  subsets: ['latin'],
  variable: '--font-poppins',
});

export const metadata: Metadata = {
  title: "<PERSON><PERSON> Bajgain - Frontend Engineer",
  description: "Creative Web Designer crafting visually stunning websites",
  icons: {
    icon: [
      {
        url: '/favicon.ico',
        sizes: 'any',
      },
    ],
    shortcut: '/favicon.ico',
    apple: '/favicon.ico',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={poppinsFont.className}>
        <ThemeProvider>
          <div className="min-h-screen bg-card/80">
            <Navbar />
            {children}
            <Footer />
            <Toaster />
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}