import { ThemeProvider } from "@/components/custom-ui/ThemeProvider"
import type { Metadata } from "next"
import "./globals.css"
import type React from "react"
import { Navbar } from "@/components/sections/Navbar"
import Footer from "@/components/sections/Footer"
import { Poppins } from 'next/font/google';
import { Toaster } from "@/components/ui/toaster";

const poppinsFont = Poppins({
  weight: '400',
  subsets: ['latin'],
  variable: '--font-poppins',
});

export const metadata: Metadata = {
  title: {
    default: "Darshan Bajgain - Frontend Developer | React, Next.js, TypeScript Expert",
    template: "%s | Darshan Bajgain - Frontend Developer"
  },
  description: "Experienced Frontend Developer specializing in React, Next.js, TypeScript, and modern web technologies. Building responsive, user-friendly web applications with clean code and exceptional user experience.",
  keywords: [
    "Darshan Bajgain",
    "Frontend Developer",
    "React Developer",
    "Next.js Developer",
    "TypeScript Developer",
    "Web Developer",
    "JavaScript Developer",
    "UI/UX Developer",
    "Responsive Web Design",
    "Modern Web Development",
    "Nepal Developer",
    "Software Engineer",
    "Web Applications",
    "Frontend Engineer"
  ],
  authors: [{ name: "Darshan Bajgain", url: "https://darshanbajgain.com.np" }],
  creator: "Darshan Bajgain",
  publisher: "Darshan Bajgain",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://darshanbajgain.com.np'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://darshanbajgain.com.np',
    title: 'Darshan Bajgain - Frontend Developer | React, Next.js, TypeScript Expert',
    description: 'Experienced Frontend Developer specializing in React, Next.js, TypeScript, and modern web technologies. Building responsive, user-friendly web applications.',
    siteName: 'Darshan Bajgain Portfolio',
    images: [
      {
        url: '/profile-pic.png',
        width: 1200,
        height: 630,
        alt: 'Darshan Bajgain - Frontend Developer',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Darshan Bajgain - Frontend Developer | React, Next.js, TypeScript Expert',
    description: 'Experienced Frontend Developer specializing in React, Next.js, TypeScript, and modern web technologies.',
    images: ['/profile-pic.png'],
    creator: '@darshan_bajgain',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    shortcut: '/favicon.ico',
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      {
        rel: 'android-chrome-192x192',
        url: '/android-chrome-192x192.png',
      },
      {
        rel: 'android-chrome-512x512',
        url: '/android-chrome-512x512.png',
      },
    ],
  },
  manifest: '/site.webmanifest',
  verification: {
    google: 'your-google-verification-code', // You'll need to add this from Google Search Console
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={poppinsFont.className}>
        <ThemeProvider>
          <div className="min-h-screen bg-gradient-to-br from-background via-card/50 to-primary/5 relative">
            {/* Global background decoration */}
            <div className="fixed inset-0 bg-grid-pattern opacity-5 dark:opacity-10 pointer-events-none"></div>
            <div className="fixed top-0 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl -z-10"></div>
            <div className="fixed bottom-0 right-1/4 w-96 h-96 bg-secondary/10 rounded-full blur-3xl -z-10"></div>
            <div className="fixed top-1/2 right-0 w-64 h-64 bg-primary/5 rounded-full blur-2xl -z-10"></div>

            <div className="relative z-10">
              <Navbar />
              {children}
              <Footer />
              <Toaster />
            </div>
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}