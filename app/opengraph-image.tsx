import { ImageResponse } from 'next/og'

export const runtime = 'edge'
export const alt = 'Darshan Bajgain - Frontend Developer'
export const size = {
  width: 1200,
  height: 630,
}
export const contentType = 'image/png'

export default async function Image() {
  return new ImageResponse(
    (
      <div
        style={{
          background: 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontFamily: 'system-ui',
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            textAlign: 'center',
          }}
        >
          <h1
            style={{
              fontSize: '72px',
              fontWeight: 'bold',
              color: '#1f2937',
              marginBottom: '20px',
            }}
          >
            Darshan Bajgain
          </h1>
          <p
            style={{
              fontSize: '36px',
              color: '#6b7280',
              marginBottom: '40px',
            }}
          >
            Frontend Developer
          </p>
          <p
            style={{
              fontSize: '24px',
              color: '#9ca3af',
              maxWidth: '800px',
            }}
          >
            React • Next.js • TypeScript • Modern Web Development
          </p>
        </div>
      </div>
    ),
    {
      ...size,
    }
  )
}
