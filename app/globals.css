@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 207, 20%, 91%;
    --foreground: 222 15% 17%;
    --card: 220 33% 98%;
    --card-foreground: 222 15% 17%;
    --popover: 220 25% 95%;
    --popover-foreground: 222 15% 17%;
    --primary: 26 85% 45%;
    --primary-foreground: 210 40% 98%;
    --secondary: 211, 12%, 38%;
    --secondary-foreground: 222 15% 17%;
    --muted: 220 14% 90%;
    --muted-foreground: 220 8% 36%;
    --accent: 220 14% 90%;
    --accent-foreground: 222 15% 17%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    --border: 215 20.2% 65.1%;
    --input: 220 13% 91%;
    --ring: 0 100% 50%;
  }

  .dark {
    --background: 222 15% 17%;
    --foreground: 210 40% 98%;
    --card: 222 18% 14%;
    --card-foreground: 210 40% 98%;
    --popover: 222 15% 17%;
    --popover-foreground: 210 40% 98%;
    --primary: 23 85% 47%; /* Kept the same red for consistency */
    --primary-foreground: 210 40% 98%;
    --secondary: 150 1% 62%;
    --secondary-foreground: 222 47% 11%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 210.2 40.6% 96%;
    --input: 217.2 32.6% 17.5%;
    --ring: 23 85% 47%;
  }
}

@layer base {
  * {
    border-color: var(--border);
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Custom Scrollbar Styles */
  ::-webkit-scrollbar {
    width: 10px;
  }


/* Track */
::-webkit-scrollbar-track {
  background: hsl(var(--background));
  border-radius: 8px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 8px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--primary)) hsl(var(--background));
  }
}
