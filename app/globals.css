@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 207, 20%, 91%;
    --foreground: 222 15% 17%;
    --card: 220 33% 98%;
    --card-foreground: 222 15% 17%;
    --popover: 220 25% 95%;
    --popover-foreground: 222 15% 17%;
    --primary: 26 85% 45%;
    --primary-foreground: 210 40% 98%;
    --secondary: 211, 12%, 38%;
    --secondary-foreground: 222 15% 17%;
    --muted: 220 14% 90%;
    --muted-foreground: 220 8% 36%;
    --accent: 220 14% 90%;
    --accent-foreground: 222 15% 17%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    --border: 215 20.2% 65.1%;
    --input: 220 13% 91%;
    --ring: 0 100% 50%;
  }

  .dark {
    --background: 222 15% 12%;
    --foreground: 210 40% 98%;
    --card: 222 18% 16%;
    --card-foreground: 210 40% 98%;
    --popover: 222 15% 16%;
    --popover-foreground: 210 40% 98%;
    --primary: 26 85% 55%; /* Slightly brighter for better contrast */
    --primary-foreground: 210 40% 98%;
    --secondary: 215 15% 25%;
    --secondary-foreground: 210 20% 85%;
    --muted: 217.2 32.6% 20%;
    --muted-foreground: 215 20.2% 70%;
    --accent: 217.2 32.6% 20%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 50%;
    --destructive-foreground: 210 40% 98%;
    --border: 215 15% 25%;
    --input: 217.2 32.6% 18%;
    --ring: 26 85% 55%;
  }
}

@layer base {
  * {
    border-color: var(--border);
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Custom Scrollbar Styles */
  ::-webkit-scrollbar {
    width: 10px;
  }


/* Track */
::-webkit-scrollbar-track {
  background: hsl(var(--background));
  border-radius: 8px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 8px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--primary)) hsl(var(--background));
  }

  /* Grid pattern background */
  .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .dark .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  }

  /* Responsive text utilities */
  @media (max-width: 640px) {
    .text-responsive-sm {
      font-size: 0.875rem;
      line-height: 1.25rem;
    }
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Scroll padding for fixed navbar */
  html {
    scroll-padding-top: 100px;
  }

  /* Better focus styles */
  *:focus-visible {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }
}
