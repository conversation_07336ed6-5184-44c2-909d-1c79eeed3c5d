import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "Portfolio - Darshan Bajgain | Frontend Developer Projects & Work",
  description: "Explore my comprehensive portfolio of React, Next.js, TypeScript, and modern web development projects. View live demos, source code, and detailed project information showcasing my frontend development skills.",
  keywords: [
    "Darshan Bajgain Portfolio",
    "Frontend Developer Projects",
    "React Projects Portfolio",
    "Next.js Projects",
    "TypeScript Projects",
    "Web Development Portfolio",
    "JavaScript Projects",
    "UI/UX Projects",
    "Responsive Web Design Projects",
    "Modern Web Applications"
  ],
  openGraph: {
    title: "Portfolio - Darshan Bajgain | Frontend Developer Projects & Work",
    description: "Explore my comprehensive portfolio of React, Next.js, TypeScript, and modern web development projects.",
    url: "https://darshanbajgain.com.np/portfolio",
    type: "website",
    images: [
      {
        url: "/profile-pic.png",
        width: 1200,
        height: 630,
        alt: "Darshan Bajgain Portfolio - Frontend Developer Projects"
      }
    ]
  },
  twitter: {
    card: "summary_large_image",
    title: "Portfolio - Darshan Bajgain | Frontend Developer Projects",
    description: "Explore my comprehensive portfolio of React, Next.js, TypeScript, and modern web development projects.",
    images: ["/profile-pic.png"]
  },
  alternates: {
    canonical: '/portfolio',
  }
}

export default function PortfolioLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <>
      {children}
    </>
  )
}
