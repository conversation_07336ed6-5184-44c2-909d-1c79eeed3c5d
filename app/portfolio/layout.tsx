import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "Portfolio - Darshan Bajgain",
  description: "A collection of projects showcasing my skills in web development, UI/UX design, and problem-solving.",
  icons: {
    icon: [
      {
        url: 'src/portfolio/favicon.ico',
        sizes: 'any',
      },
    ],
    shortcut: 'src/portfolio/favicon.ico',
  },
}

export default function PortfolioLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <>
      {children}
    </>
  )
}
