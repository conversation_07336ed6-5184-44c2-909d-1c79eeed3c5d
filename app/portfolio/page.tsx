"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { ExternalLink, Github, Filter, Search, X, ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { projects } from "@/data/projects"

export default function PortfolioPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [filteredProjects, setFilteredProjects] = useState(projects)

  // Get all unique tags from projects
  const allTags = Array.from(
    new Set(
      projects.flatMap((project: typeof projects[0]) => project.tags || [])
    )
  ).sort()

  // Filter projects based on search query and selected tags
  useEffect(() => {
    const filtered = projects.filter((project: typeof projects[0]) => {
      const matchesSearch =
        searchQuery === "" ||
        project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (project.tags && project.tags.some((tag: string) =>
          tag.toLowerCase().includes(searchQuery.toLowerCase())
        ))

      const matchesTags =
        selectedTags.length === 0 ||
        (project.tags && selectedTags.every(tag => project.tags && project.tags.includes(tag)))

      return matchesSearch && matchesTags
    })

    setFilteredProjects(filtered)
  }, [searchQuery, selectedTags])

  // Toggle tag selection
  const toggleTag = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    )
  }

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery("")
    setSelectedTags([])
  }

  return (
    <div className="min-h-screen bg-card/80 py-12">
      <div className="w-full bg-card/80 backdrop-blur-sm py-12">
        <div className="w-full max-w-screen-lg mx-auto px-6 sm:px-8">
          <div className="my-4 flex items-end justify-end">
            <Link
              href="/"
              className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors mb-4 group"
            >
              <ArrowLeft className="h-4 w-4 transition-transform group-hover:-translate-x-1" />
              <span className="font-medium">Back to Home</span>
            </Link>
          </div>
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-8">
            <div>
              <h1 className="text-3xl sm:text-4xl font-bold text-primary">Project Portfolio</h1>
              <p className="text-muted-foreground mt-2 max-w-2xl">
                A collection of my projects showcasing my skills as a web developer.
              </p>
            </div>
          </div>

          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4 mb-8">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search projects..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 border-primary/10 focus:border-primary/30 bg-background rounded-none"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7"
                  onClick={() => setSearchQuery("")}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            <div className="flex items-center gap-2">
              {selectedTags.length > 0 || searchQuery ? (
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "border-primary/20 transition-colors",
                    (selectedTags.length > 0 || searchQuery)
                      ? "bg-primary text-primary-foreground hover:bg-primary/90"
                      : "hover:bg-primary/5"
                  )}
                  onClick={clearFilters}
                >
                  <Filter className="mr-2 h-4 w-4" />
                  {"Clear"}
                </Button>) : null}
            </div>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-2 mb-8">
            {allTags.map(tag => (
              <Badge
                key={tag}
                variant={selectedTags.includes(tag) ? "default" : "outline"}
                className={cn(
                  "cursor-pointer transition-all",
                  selectedTags.includes(tag)
                    ? "bg-primary text-primary-foreground hover:bg-primary/80 font-medium"
                    : "hover:bg-primary/20 border-primary/30 text-primary font-medium"
                )}
                onClick={() => toggleTag(tag)}
              >
                {tag}
                {selectedTags.includes(tag) && (
                  <X className="ml-1 h-3 w-3" />
                )}
              </Badge>
            ))}
          </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {filteredProjects.length > 0 ? (
              filteredProjects.map((project, index) => (
                <ProjectCard key={project.id} project={project} index={index} />
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <p className="text-muted-foreground">No projects match your filters.</p>
                <Button
                  variant="link"
                  onClick={clearFilters}
                  className="mt-2"
                >
                  Clear all filters
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

interface ProjectCardProps {
  project: typeof projects[0]
  index: number
}

function ProjectCard({ project, index }: ProjectCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="group flex bg-background flex-col overflow-hidden transition-all duration-300 h-full"
    >
      <div className="relative aspect-video w-full overflow-hidden">
        <Image
          src={project.image || "/placeholder.svg"}
          alt={project.title}
          fill
          className="object-fit z-10 transition-transform duration-500 group-hover:scale-105 p-6"
        />
      </div>

      <div className="flex-1 p-6 space-y-2">
        <h3 className="text-lg md:text-xl font-semibold text-foreground group-hover:text-primary transition-colors duration-300 line-clamp-3 text-ellipsis">
          {project.title}
        </h3>

        <p className="text-sm text-muted-foreground mt-3 line-clamp-5 leading-relaxed">
          {project.description}
        </p>

        {project.tags && (
          <div className="flex flex-wrap gap-2 mt-4">
            {project.tags.slice(0, 5).map(tag => (
              <span key={tag} className="text-xs px-2.5 py-1 bg-primary/20 text-primary rounded-full font-medium">
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>

      <div className="p-6 pt-0 mt-auto flex gap-3">
        {project.githubUrl && (
          <Button
            asChild
            variant="outline"
            size="sm"
            className="flex-1 border-primary/30 hover:bg-primary hover:text-primary-foreground transition-colors duration-300"
          >
            <Link
              href={project.githubUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 justify-center"
            >
              <Github className="h-4 w-4" />
              <span className="inline">Code</span>
            </Link>
          </Button>
        )}

        <Button
          asChild
          variant="default"
          size="sm"
          className="flex-1 bg-primary hover:bg-primary/90 transition-colors duration-300"
        >
          <Link
            href={project.demoUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-2 justify-center"
          >
            <ExternalLink className="h-4 w-4" />
            <span className="inline">Demo</span>
          </Link>
        </Button>
      </div>
    </motion.div>
  )
}
