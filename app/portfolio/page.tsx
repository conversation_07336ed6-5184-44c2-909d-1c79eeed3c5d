"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { ExternalLink, Filter, Search, X, Code2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { projects } from "@/data/projects"

export default function PortfolioPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [filteredProjects, setFilteredProjects] = useState(projects)

  // Get all unique tags from projects
  const allTags = Array.from(
    new Set(
      projects.flatMap((project: typeof projects[0]) => project.tags || [])
    )
  ).sort()

  // Filter projects based on search query and selected tags
  useEffect(() => {
    const filtered = projects.filter((project: typeof projects[0]) => {
      const matchesSearch =
        searchQuery === "" ||
        project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (project.tags && project.tags.some((tag: string) =>
          tag.toLowerCase().includes(searchQuery.toLowerCase())
        ))

      const matchesTags =
        selectedTags.length === 0 ||
        (project.tags && selectedTags.every(tag => project.tags && project.tags.includes(tag)))

      return matchesSearch && matchesTags
    })

    setFilteredProjects(filtered)
  }, [searchQuery, selectedTags])

  // Toggle tag selection
  const toggleTag = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    )
  }

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery("")
    setSelectedTags([])
  }

  return (
    <div className="min-h-screen py-12 relative">
      <div className="w-full backdrop-blur-sm py-16 relative z-10">
        <div className="w-full max-w-screen-lg mx-auto px-4 sm:px-6 lg:px-8">
          {/* <div className="my-4 flex items-end justify-end">
            <Link
              href="/"
              className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors mb-4 group px-4 py-2 rounded-lg border border-primary/30 hover:bg-primary/10 font-semibold"
            >
              <ArrowLeft className="h-4 w-4 transition-transform group-hover:-translate-x-1" />
              <span>Back to Home</span>
            </Link>
          </div> */}
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-6 mb-16">
            <div className="text-center md:text-left">
              <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">
                <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                  Project Portfolio
                </span>
              </h1>
              <p className="text-foreground/80 text-base sm:text-lg max-w-2xl font-medium">
                A collection of my projects showcasing my skills as a web developer.
              </p>
            </div>
          </div>

          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4 mb-10">
            <div className="relative flex-1">
              <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <Input
                placeholder="Search projects..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-12 py-3 border-primary/40 focus:border-primary/40 bg-background/80 rounded-xl shadow-sm text-base"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 hover:bg-primary/10"
                  onClick={() => setSearchQuery("")}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            <div className="flex items-center gap-2">
              {selectedTags.length > 0 || searchQuery ? (
                <Button
                  variant="outline"
                  size="default"
                  className={cn(
                    "border-primary/30 transition-colors shadow-sm",
                    (selectedTags.length > 0 || searchQuery)
                      ? "bg-primary text-primary-foreground hover:bg-primary/90 font-semibold"
                      : "hover:bg-primary/10"
                  )}
                  onClick={clearFilters}
                >
                  <Filter className="mr-2 h-4 w-4" />
                  {"Clear Filters"}
                </Button>) : null}
            </div>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-3 mb-10">
            {allTags.map(tag => (
              <Badge
                key={tag}
                variant={selectedTags.includes(tag) ? "default" : "outline"}
                className={cn(
                  "cursor-pointer transition-all px-4 py-2 text-sm shadow-sm",
                  selectedTags.includes(tag)
                    ? "bg-primary text-primary-foreground hover:bg-primary/90 font-semibold border-primary"
                    : "hover:bg-primary/15 border-primary/40 text-primary font-medium bg-background/80 backdrop-blur-sm"
                )}
                onClick={() => toggleTag(tag)}
              >
                {tag}
                {selectedTags.includes(tag) && (
                  <X className="ml-2 h-3 w-3" />
                )}
              </Badge>
            ))}
          </div>

          {/* Projects Masonry Grid */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="columns-1 md:columns-2 lg:columns-3 gap-6 space-y-6"
          >
            {filteredProjects.length > 0 ? (
              filteredProjects.map((project, index) => (
                <div key={project.id} className="break-inside-avoid mb-6">
                  <ProjectCard project={project} index={index} />
                </div>
              ))
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="col-span-full text-center py-16"
              >
                <div className="max-w-md mx-auto space-y-4">
                  <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                    <Search className="w-8 h-8 text-primary/60" />
                  </div>
                  <p className="text-foreground/70 text-base sm:text-lg">No projects match your filters.</p>
                  <Button
                    variant="outline"
                    onClick={clearFilters}
                    className="border-primary/30 hover:bg-primary/10 hover:border-primary/50 transition-all duration-300"
                  >
                    Clear all filters
                  </Button>
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  )
}

interface ProjectCardProps {
  project: typeof projects[0]
  index: number
}

function ProjectCard({ project, index }: ProjectCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="group relative"
    >
      {/* Card container with modern design */}
      <div className="relative bg-card/90 backdrop-blur-sm rounded-2xl border border-primary/20 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden h-full flex flex-col">

        {/* Image section */}
        <div className="relative aspect-[16/10] overflow-hidden">
          <Image
            src={project.image || "/placeholder.svg"}
            alt={project.title}
            fill
            className="object-contain transition-transform duration-500 group-hover:scale-105"
          />

          {/* Overlay on hover */}
          <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <div className="flex gap-3">
              <Link
                href={project.demoUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 px-4 py-2 bg-white/90 backdrop-blur-sm rounded-full text-gray-900 font-medium hover:bg-white transition-colors duration-200"
              >
                <ExternalLink className="w-4 h-4" />
                <span>Live Demo</span>
              </Link>

              {project.githubUrl && (
                <Link
                  href={project.githubUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 px-4 py-2 bg-primary/90 backdrop-blur-sm rounded-full text-white font-medium hover:bg-primary transition-colors duration-200"
                >
                  <Code2 className="w-4 h-4" />
                  <span>Code</span>
                </Link>
              )}
            </div>
          </div>

          {/* Status badge */}
          <div className="absolute top-3 left-3">
            <Badge className="bg-green-500/90 text-white border-0 shadow-md">
              <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse" />
              Live
            </Badge>
          </div>
        </div>

        {/* Content section */}
        <div className="p-5 flex-1 flex flex-col">
          <div className="space-y-3 flex-1">
            <h3 className="text-lg font-bold text-foreground group-hover:text-primary transition-colors duration-300 line-clamp-3">
              {project.title}
            </h3>

            <p className="text-sm text-muted-foreground leading-relaxed line-clamp-5 h-[8em]">
              {project.description}
            </p>
          </div>

          {/* Technology tags - limit to 3 */}
          {project.tags && (
            <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t border-primary/10">
              {project.tags.slice(0, 3).map((tag: string) => (
                <Badge
                  key={tag}
                  variant="secondary"
                  className="text-xs px-2.5 py-1 bg-primary/10 text-primary border border-primary/20 font-medium"
                >
                  {tag}
                </Badge>
              ))}
              {/* {project.tags.length > 3 && (
                <Badge
                  variant="outline"
                  className="text-xs px-2.5 py-1 text-muted-foreground"
                >
                  +{project.tags.length - 3}
                </Badge>
              )} */}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  )
}
