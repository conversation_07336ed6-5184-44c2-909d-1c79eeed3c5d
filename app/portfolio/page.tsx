"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { ExternalLink, Github, Filter, Search, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { projects } from "@/data/projects"

export default function PortfolioPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [filteredProjects, setFilteredProjects] = useState(projects)

  // Get all unique tags from projects
  const allTags = Array.from(
    new Set(
      projects.flatMap((project: typeof projects[0]) => project.tags || [])
    )
  ).sort()

  // Filter projects based on search query and selected tags
  useEffect(() => {
    const filtered = projects.filter((project: typeof projects[0]) => {
      const matchesSearch =
        searchQuery === "" ||
        project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (project.tags && project.tags.some((tag: string) =>
          tag.toLowerCase().includes(searchQuery.toLowerCase())
        ))

      const matchesTags =
        selectedTags.length === 0 ||
        (project.tags && selectedTags.every(tag => project.tags && project.tags.includes(tag)))

      return matchesSearch && matchesTags
    })

    setFilteredProjects(filtered)
  }, [searchQuery, selectedTags])

  // Toggle tag selection
  const toggleTag = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    )
  }

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery("")
    setSelectedTags([])
  }

  return (
    <div className="min-h-screen py-12 relative">
      <div className="w-full backdrop-blur-sm py-16 relative z-10">
        <div className="w-full max-w-screen-lg mx-auto px-4 sm:px-6 lg:px-8">
          {/* <div className="my-4 flex items-end justify-end">
            <Link
              href="/"
              className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors mb-4 group px-4 py-2 rounded-lg border border-primary/30 hover:bg-primary/10 font-semibold"
            >
              <ArrowLeft className="h-4 w-4 transition-transform group-hover:-translate-x-1" />
              <span>Back to Home</span>
            </Link>
          </div> */}
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-6 mb-16">
            <div className="text-center md:text-left">
              <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">
                <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                  Project Portfolio
                </span>
              </h1>
              <p className="text-foreground/80 text-base sm:text-lg max-w-2xl font-medium">
                A collection of my projects showcasing my skills as a web developer.
              </p>
            </div>
          </div>

          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4 mb-10">
            <div className="relative flex-1">
              <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <Input
                placeholder="Search projects..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-12 py-3 border-primary/40 focus:border-primary/40 bg-background/80 rounded-xl shadow-sm text-base"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 hover:bg-primary/10"
                  onClick={() => setSearchQuery("")}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            <div className="flex items-center gap-2">
              {selectedTags.length > 0 || searchQuery ? (
                <Button
                  variant="outline"
                  size="default"
                  className={cn(
                    "border-primary/30 transition-colors shadow-sm",
                    (selectedTags.length > 0 || searchQuery)
                      ? "bg-primary text-primary-foreground hover:bg-primary/90 font-semibold"
                      : "hover:bg-primary/10"
                  )}
                  onClick={clearFilters}
                >
                  <Filter className="mr-2 h-4 w-4" />
                  {"Clear Filters"}
                </Button>) : null}
            </div>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-3 mb-10">
            {allTags.map(tag => (
              <Badge
                key={tag}
                variant={selectedTags.includes(tag) ? "default" : "outline"}
                className={cn(
                  "cursor-pointer transition-all px-4 py-2 text-sm shadow-sm",
                  selectedTags.includes(tag)
                    ? "bg-primary text-primary-foreground hover:bg-primary/90 font-semibold border-primary"
                    : "hover:bg-primary/15 border-primary/40 text-primary font-medium bg-background/80 backdrop-blur-sm"
                )}
                onClick={() => toggleTag(tag)}
              >
                {tag}
                {selectedTags.includes(tag) && (
                  <X className="ml-2 h-3 w-3" />
                )}
              </Badge>
            ))}
          </div>

          {/* Projects Grid */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 sm:gap-8"
          >
            {filteredProjects.length > 0 ? (
              filteredProjects.map((project, index) => (
                <ProjectCard key={project.id} project={project} index={index} />
              ))
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="col-span-full text-center py-16"
              >
                <div className="max-w-md mx-auto space-y-4">
                  <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                    <Search className="w-8 h-8 text-primary/60" />
                  </div>
                  <p className="text-foreground/70 text-base sm:text-lg">No projects match your filters.</p>
                  <Button
                    variant="outline"
                    onClick={clearFilters}
                    className="border-primary/30 hover:bg-primary/10 hover:border-primary/50 transition-all duration-300"
                  >
                    Clear all filters
                  </Button>
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  )
}

interface ProjectCardProps {
  project: typeof projects[0]
  index: number
}

function ProjectCard({ project, index }: ProjectCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{
        duration: 0.6,
        delay: index * 0.1,
        type: "spring",
        stiffness: 100
      }}
      className="group relative h-full"
    >
      {/* Main card container */}
      <div className="relative h-full bg-gradient-to-br from-card/95 via-card to-primary/5 rounded-3xl border border-primary/20 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden backdrop-blur-sm hover:-translate-y-2 hover:border-primary/40">

        {/* Animated background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

        {/* Floating elements */}
        <div className="absolute top-4 right-4 z-20 flex gap-2">
          {project.githubUrl && (
            <Link
              href={project.githubUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center w-9 h-9 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 group/btn"
            >
              <Github className="w-4 h-4 text-gray-700 group-hover/btn:text-gray-900" />
            </Link>
          )}
          <Link
            href={project.demoUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center w-9 h-9 bg-primary/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-primary hover:scale-110 transition-all duration-300 group/btn"
          >
            <ExternalLink className="w-4 h-4 text-white" />
          </Link>
        </div>

        {/* Image section with overlay */}
        <div className="relative h-48 sm:h-56 overflow-hidden rounded-t-3xl">
          <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent z-10" />
          <Image
            src={project.image || "/placeholder.svg"}
            alt={project.title}
            fill
            className="object-cover transition-transform duration-700 group-hover:scale-110"
          />

          {/* Project status badge */}
          <div className="absolute bottom-4 left-4 z-20">
            <Badge className="bg-green-500/90 text-white border-0 shadow-lg backdrop-blur-sm">
              <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse" />
              Live
            </Badge>
          </div>
        </div>

        {/* Content section */}
        <div className="relative p-6 space-y-4">
          {/* Title and description */}
          <div className="space-y-3">
            <h3 className="text-xl sm:text-2xl font-bold text-foreground group-hover:text-primary transition-colors duration-300 line-clamp-2 leading-tight">
              {project.title}
            </h3>
            <p className="text-sm sm:text-base text-muted-foreground leading-relaxed line-clamp-3">
              {project.description}
            </p>
          </div>

          {/* Technology tags */}
          {project.tags && (
            <div className="space-y-2">
              <div className="flex flex-wrap gap-2">
                {project.tags.slice(0, 6).map((tag: string) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="text-xs px-3 py-1.5 bg-primary/10 text-primary border border-primary/20 hover:bg-primary/20 transition-all duration-200 font-medium"
                  >
                    {tag}
                  </Badge>
                ))}
                {project.tags.length > 6 && (
                  <Badge
                    variant="outline"
                    className="text-xs px-3 py-1.5 text-muted-foreground border-dashed"
                  >
                    +{project.tags.length - 6} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Action buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              asChild
              variant="outline"
              className="flex-1 border-primary/30 hover:bg-primary/10 hover:border-primary/50 transition-all duration-300 group/btn"
            >
              <Link
                href={project.demoUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center gap-2"
              >
                <ExternalLink className="w-4 h-4" />
                <span>Live Demo</span>
              </Link>
            </Button>

            {project.githubUrl && (
              <Button
                asChild
                variant="default"
                className="flex-1 bg-primary hover:bg-primary/90 transition-all duration-300 shadow-md group/btn"
              >
                <Link
                  href={project.githubUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-center gap-2"
                >
                  <Github className="w-4 h-4" />
                  <span>Source</span>
                </Link>
              </Button>
            )}
          </div>
        </div>

        {/* Bottom gradient border */}
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary via-secondary to-primary opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      </div>
    </motion.div>
  )
}
