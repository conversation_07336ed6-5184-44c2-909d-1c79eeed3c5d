"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { ExternalLink, Github, Filter, Search, X, ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { projects } from "@/data/projects"

export default function PortfolioPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [filteredProjects, setFilteredProjects] = useState(projects)

  // Get all unique tags from projects
  const allTags = Array.from(
    new Set(
      projects.flatMap((project: typeof projects[0]) => project.tags || [])
    )
  ).sort()

  // Filter projects based on search query and selected tags
  useEffect(() => {
    const filtered = projects.filter((project: typeof projects[0]) => {
      const matchesSearch =
        searchQuery === "" ||
        project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (project.tags && project.tags.some((tag: string) =>
          tag.toLowerCase().includes(searchQuery.toLowerCase())
        ))

      const matchesTags =
        selectedTags.length === 0 ||
        (project.tags && selectedTags.every(tag => project.tags && project.tags.includes(tag)))

      return matchesSearch && matchesTags
    })

    setFilteredProjects(filtered)
  }, [searchQuery, selectedTags])

  // Toggle tag selection
  const toggleTag = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    )
  }

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery("")
    setSelectedTags([])
  }

  return (
    <div className="min-h-screen py-12 relative">
      <div className="w-full backdrop-blur-sm py-16 relative z-10">
        <div className="w-full max-w-screen-lg mx-auto px-4 sm:px-6 lg:px-8">
          <div className="my-4 flex items-end justify-end">
            <Link
              href="/"
              className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors mb-4 group px-4 py-2 rounded-lg border border-primary/30 hover:bg-primary/10 font-semibold"
            >
              <ArrowLeft className="h-4 w-4 transition-transform group-hover:-translate-x-1" />
              <span>Back to Home</span>
            </Link>
          </div>
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-6 mb-16">
            <div className="text-center md:text-left">
              <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">
                <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                  Project Portfolio
                </span>
              </h1>
              <p className="text-foreground/80 text-base sm:text-lg max-w-2xl font-medium">
                A collection of my projects showcasing my skills as a web developer.
              </p>
            </div>
          </div>

          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4 mb-10">
            <div className="relative flex-1">
              <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <Input
                placeholder="Search projects..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-12 py-3 border-primary/20 focus:border-primary/40 bg-background/80 backdrop-blur-sm rounded-xl shadow-sm text-base"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 hover:bg-primary/10"
                  onClick={() => setSearchQuery("")}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            <div className="flex items-center gap-2">
              {selectedTags.length > 0 || searchQuery ? (
                <Button
                  variant="outline"
                  size="default"
                  className={cn(
                    "border-primary/30 transition-colors shadow-sm",
                    (selectedTags.length > 0 || searchQuery)
                      ? "bg-primary text-primary-foreground hover:bg-primary/90 font-semibold"
                      : "hover:bg-primary/10"
                  )}
                  onClick={clearFilters}
                >
                  <Filter className="mr-2 h-4 w-4" />
                  {"Clear Filters"}
                </Button>) : null}
            </div>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-3 mb-10">
            {allTags.map(tag => (
              <Badge
                key={tag}
                variant={selectedTags.includes(tag) ? "default" : "outline"}
                className={cn(
                  "cursor-pointer transition-all px-4 py-2 text-sm shadow-sm",
                  selectedTags.includes(tag)
                    ? "bg-primary text-primary-foreground hover:bg-primary/90 font-semibold border-primary"
                    : "hover:bg-primary/15 border-primary/40 text-primary font-medium bg-background/80 backdrop-blur-sm"
                )}
                onClick={() => toggleTag(tag)}
              >
                {tag}
                {selectedTags.includes(tag) && (
                  <X className="ml-2 h-3 w-3" />
                )}
              </Badge>
            ))}
          </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-10">
            {filteredProjects.length > 0 ? (
              filteredProjects.map((project, index) => (
                <ProjectCard key={project.id} project={project} index={index} />
              ))
            ) : (
              <div className="col-span-full text-center py-16">
                <p className="text-foreground/70 text-base sm:text-lg mb-4">No projects match your filters.</p>
                <Button
                  variant="outline"
                  onClick={clearFilters}
                  className="border-primary/30 hover:bg-primary/10"
                >
                  Clear all filters
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

interface ProjectCardProps {
  project: typeof projects[0]
  index: number
}

function ProjectCard({ project, index }: ProjectCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="group flex bg-card/90 backdrop-blur-sm flex-col overflow-hidden transition-all duration-300 h-full rounded-2xl border border-primary/20 shadow-lg hover:shadow-xl hover:border-primary/30"
    >
      <div className="relative aspect-video w-full overflow-hidden rounded-t-2xl bg-gradient-to-br from-primary/5 to-secondary/5">
        <Image
          src={project.image || "/placeholder.svg"}
          alt={project.title}
          fill
          className="object-fit z-10 transition-transform duration-500 group-hover:scale-105 p-6"
        />
      </div>

      <div className="flex-1 p-4 sm:p-6 space-y-3">
        <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-foreground group-hover:text-primary transition-colors duration-300 line-clamp-2 text-ellipsis">
          {project.title}
        </h3>

        <p className="text-sm sm:text-base text-foreground/80 mt-3 line-clamp-3 sm:line-clamp-4 leading-relaxed font-medium">
          {project.description}
        </p>

        {project.tags && (
          <div className="flex flex-wrap gap-1.5 sm:gap-2 mt-4 sm:mt-5">
            {project.tags.slice(0, 5).map(tag => (
              <span key={tag} className="text-xs px-2.5 sm:px-3 py-1 sm:py-1.5 bg-primary/25 text-primary rounded-full font-semibold border border-primary/30">
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>

      <div className="p-4 sm:p-6 pt-0 mt-auto flex gap-3 sm:gap-4">
        {project.githubUrl && (
          <Button
            asChild
            variant="outline"
            size="sm"
            className="flex-1 border-primary/40 hover:bg-primary hover:text-primary-foreground transition-colors duration-300 shadow-sm font-semibold text-xs sm:text-sm"
          >
            <Link
              href={project.githubUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-1.5 sm:gap-2 justify-center"
            >
              <Github className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="inline">Code</span>
            </Link>
          </Button>
        )}

        <Button
          asChild
          variant="default"
          size="sm"
          className="flex-1 bg-primary hover:bg-primary/90 transition-colors duration-300 shadow-md font-semibold text-xs sm:text-sm"
        >
          <Link
            href={project.demoUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-1.5 sm:gap-2 justify-center"
          >
            <ExternalLink className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="inline">Demo</span>
          </Link>
        </Button>
      </div>
    </motion.div>
  )
}
