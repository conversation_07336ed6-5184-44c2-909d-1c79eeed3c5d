import Blogs from "@/components/sections/Blogs"
import Contact from "@/components/sections/Contact"
import { Hero } from "@/components/sections/Hero"
import ProjectsSection from "@/components/sections/Project"
// import Tools from "@/components/sections/Tools"
import WorkExperienceTimeline from "@/components/sections/WorkExprienceTimeline"
import Head from "next/head"

export default function HomePage() {


  return <>
    <Head>
      <title>Darshan Bajgain - Frontend Developer</title>
      <meta name="description" content="Frontend Developer specializing in React, Next.js, and TypeScript. Check out my projects and experience!" />
      <meta name="keywords" content="Frontend Developer, React, Next.js, TypeScript, Zustand, Tailwind CSS, shadcn/ui" />
      <meta name="author" content="Darshan Bajgain" />
      <meta property="og:title" content="Darshan Bajgain - Frontend Developer" />
      <meta property="og:description" content="Check out my portfolio and projects." />
      <meta property="og:image" content="/profile-pic.png" />
      <meta property="og:url" content="https://darshanbajgain.com.np" />
    </Head>
    <main className="min-h-screen pt-24">
      <div id="home">
        <Hero />
      </div>
      <div id="experience">
        <WorkExperienceTimeline />
      </div>
      <div id="projects">
        <ProjectsSection />
      </div>
      {/* <div id="tools">
        <Tools />
      </div> */}
      <div id="blogs">
        <Blogs />
      </div>
      <div id="contact">
        <Contact />
      </div>
    </main>
  </>
}
