import Blogs from "@/components/sections/Blogs"
import Contact from "@/components/sections/Contact"
import { Hero } from "@/components/sections/Hero"
import ProjectsSection from "@/components/sections/Project"
// import Tools from "@/components/sections/Tools"
import WorkExperienceTimeline from "@/components/sections/WorkExprienceTimeline"
import StructuredData from "@/components/seo/StructuredData"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Darshan Bajgain - Frontend Developer | React, Next.js, TypeScript Expert",
  description: "Experienced Frontend Developer from Nepal specializing in React, Next.js, TypeScript, and modern web technologies. View my portfolio, projects, and professional experience.",
  keywords: [
    "Darshan Bajgain",
    "Frontend Developer Nepal",
    "React Developer Nepal",
    "Next.js Developer",
    "TypeScript Expert",
    "Web Developer Portfolio",
    "JavaScript Developer",
    "UI/UX Developer",
    "Responsive Web Design",
    "Modern Web Development"
  ],
  openGraph: {
    title: "Darshan Bajgain - Frontend Developer | React, Next.js, TypeScript Expert",
    description: "Experienced Frontend Developer from Nepal specializing in React, Next.js, TypeScript, and modern web technologies.",
    url: "https://darshanbajgain.com.np",
    type: "website",
    images: [
      {
        url: "/profile-pic.png",
        width: 1200,
        height: 630,
        alt: "Darshan Bajgain - Frontend Developer"
      }
    ]
  },
  twitter: {
    card: "summary_large_image",
    title: "Darshan Bajgain - Frontend Developer | React, Next.js, TypeScript Expert",
    description: "Experienced Frontend Developer from Nepal specializing in React, Next.js, TypeScript, and modern web technologies.",
    images: ["/profile-pic.png"]
  }
}

export default function HomePage() {


  return <>
    <StructuredData />
    <main className="min-h-screen pt-24" role="main">
      <header id="home">
        <Hero />
      </header>
      <section id="experience" aria-labelledby="experience-heading">
        <WorkExperienceTimeline />
      </section>
      <section id="projects" aria-labelledby="projects-heading">
        <ProjectsSection />
      </section>
      {/* <section id="tools" aria-labelledby="tools-heading">
        <Tools />
      </section> */}
      <section id="blogs" aria-labelledby="blogs-heading">
        <Blogs />
      </section>
      <section id="contact" aria-labelledby="contact-heading">
        <Contact />
      </section>
    </main>
  </>
}
